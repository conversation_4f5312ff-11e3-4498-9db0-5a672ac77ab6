using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Metabolomics.MsLima.Bean;

namespace Metabolomics.MsLima.Services
{
    /// <summary>
    /// Service to manage dynamic column visibility based on available MSP data fields
    /// </summary>
    public static class ColumnVisibilityService
    {
        /// <summary>
        /// Mapping between CompoundBean property names and DataGrid column headers
        /// </summary>
        private static readonly Dictionary<string, string> PropertyToColumnHeaderMap = new Dictionary<string, string>
        {
            // Core identification fields
            { nameof(CompoundBean.Id), "ID" },
            { nameof(CompoundBean.Name), "Name" },
            { nameof(CompoundBean.RetentionTimes), "Retention time" },
            { nameof(CompoundBean.PrecursorMz), "Precursor MZ" },
            { nameof(CompoundBean.PrecursorType), "Precursor type" },
            { nameof(CompoundBean.IonMode), "Ion mode" },
            
            // Chemical identifiers
            { nameof(CompoundBean.CAS), "CAS" },
            { nameof(CompoundBean.CID), "CID" },
            { nameof(CompoundBean.Formula), "Formula" },
            { nameof(CompoundBean.ExactMass), "Exact mass" },
            
            // Toxicological/Regulatory fields
            { nameof(CompoundBean.Cramerrules), "Cramer rules" },
            { nameof(CompoundBean.SVHC), "SVHC" },
            { nameof(CompoundBean.CMR), "CMR" },
            { nameof(CompoundBean.CMRSuspect), "CMR suspect" },
            { nameof(CompoundBean.EDC), "EDC" },
            { nameof(CompoundBean.IARC), "IARC" },
            { nameof(CompoundBean.Eusml), "EU SML" },
            { nameof(CompoundBean.ChinaSml), "China SML" },
            { nameof(CompoundBean.Carcinogenicity_ISS), "Carcinogenicity ISS" },
            { nameof(CompoundBean.DNA_alerts_OASIS), "DNA alerts OASIS" },
            { nameof(CompoundBean.DNA_binding_OASIS), "DNA binding OASIS" },
            { nameof(CompoundBean.DNA_binding_OECD), "DNA binding OECD" },
            { nameof(CompoundBean.Protein_binding_alerts_OASIS), "Protein binding alerts OASIS" },
            { nameof(CompoundBean.Vitro_mutagenicity_alerts_ISS), "Vitro mutagenicity alerts ISS" },
            { nameof(CompoundBean.Vivo_mutagenicity_alerts_ISS), "Vivo mutagenicity alerts ISS" },
            
            // Chemical structure fields
            { nameof(CompoundBean.Smiles), "SMILES" },
            { nameof(CompoundBean.InChI), "InChI" },
            { nameof(CompoundBean.InChIKey), "InChIKey" },
            { nameof(CompoundBean.IUPACName), "IUPAC name" },
            
            // Classification fields
            { nameof(CompoundBean.Superclass), "Superclass" },
            { nameof(CompoundBean.Class), "Class" },
            { nameof(CompoundBean.Subclass), "Subclass" },
            { nameof(CompoundBean.Ontology), "Ontology" },
            
            // Physicochemical properties
            { nameof(CompoundBean.XLogP), "XLogP" },
            { nameof(CompoundBean.MLogP), "MLogP" },
            { nameof(CompoundBean.ALogP), "ALogP" },
            { nameof(CompoundBean.TopoPSA), "Topo PSA" },
            { nameof(CompoundBean.NumBond), "Num bonds" },
            { nameof(CompoundBean.NumAtom), "Num atoms" },
            
            // Metadata fields
            { nameof(CompoundBean.Authors), "Authors" },
            { nameof(CompoundBean.Instrument), "Instrument" },
            { nameof(CompoundBean.InstrumentType), "Instrument type" },
            { nameof(CompoundBean.CollisionEnergy), "Collision energy" },
            { nameof(CompoundBean.Date), "Date" },
            { nameof(CompoundBean.Num_peaks), "Num peaks" }
        };

        /// <summary>
        /// Core columns that should always be visible regardless of MSP content
        /// </summary>
        private static readonly HashSet<string> AlwaysVisibleColumns = new HashSet<string>
        {
            "ID",
            "Name"
        };

        /// <summary>
        /// Analyzes the first compound to determine which columns should be visible
        /// </summary>
        /// <param name="compounds">List of compounds from the loaded MSP file</param>
        /// <returns>Set of column headers that should be visible</returns>
        public static HashSet<string> DetermineVisibleColumns(List<CompoundBean> compounds)
        {
            var visibleColumns = new HashSet<string>(AlwaysVisibleColumns);
            
            if (compounds == null || compounds.Count == 0)
            {
                return visibleColumns;
            }

            // Analyze the first compound to determine available fields
            var firstCompound = compounds[0];
            
            foreach (var mapping in PropertyToColumnHeaderMap)
            {
                var propertyName = mapping.Key;
                var columnHeader = mapping.Value;
                
                // Skip already included core columns
                if (AlwaysVisibleColumns.Contains(columnHeader))
                    continue;
                
                // Check if the property has meaningful data
                if (HasMeaningfulData(firstCompound, propertyName))
                {
                    visibleColumns.Add(columnHeader);
                }
            }
            
            return visibleColumns;
        }

        /// <summary>
        /// Applies column visibility to a DataGrid based on visible columns set
        /// </summary>
        /// <param name="dataGrid">The DataGrid to modify</param>
        /// <param name="visibleColumns">Set of column headers that should be visible</param>
        public static void ApplyColumnVisibility(DataGrid dataGrid, HashSet<string> visibleColumns)
        {
            if (dataGrid?.Columns == null)
                return;

            foreach (DataGridColumn column in dataGrid.Columns)
            {
                var header = column.Header?.ToString();
                if (!string.IsNullOrEmpty(header))
                {
                    column.Visibility = visibleColumns.Contains(header)
                        ? System.Windows.Visibility.Visible
                        : System.Windows.Visibility.Collapsed;
                }
            }
        }

        /// <summary>
        /// Applies filter control visibility to match the visible columns
        /// This method properly handles Grid column definitions to maintain alignment
        /// </summary>
        /// <param name="filterGrid">The Grid containing filter controls</param>
        /// <param name="visibleColumns">Set of column headers that should be visible</param>
        public static void ApplyFilterControlVisibility(Grid filterGrid, HashSet<string> visibleColumns)
        {
            if (filterGrid?.Children == null || filterGrid.ColumnDefinitions == null)
                return;

            // Store original column information
            var originalColumns = new List<(ColumnDefinition colDef, TextBox textBox, int originalIndex)>();

            for (int i = 0; i < filterGrid.ColumnDefinitions.Count; i++)
            {
                var textBox = filterGrid.Children.OfType<TextBox>()
                    .FirstOrDefault(tb => Grid.GetColumn(tb) == i);

                if (textBox != null)
                {
                    originalColumns.Add((filterGrid.ColumnDefinitions[i], textBox, i));
                }
            }

            // Clear existing structure
            filterGrid.ColumnDefinitions.Clear();
            filterGrid.Children.Clear();

            // Rebuild with only visible columns
            int newColumnIndex = 0;
            foreach (var (colDef, textBox, originalIndex) in originalColumns)
            {
                var tag = textBox.Tag?.ToString();
                if (!string.IsNullOrEmpty(tag) && visibleColumns.Contains(tag))
                {
                    // Add column definition with original width
                    var newColDef = new ColumnDefinition();
                    newColDef.Width = GetOriginalColumnWidth(originalIndex);
                    filterGrid.ColumnDefinitions.Add(newColDef);

                    // Set new column position and add TextBox
                    Grid.SetColumn(textBox, newColumnIndex);
                    filterGrid.Children.Add(textBox);

                    newColumnIndex++;
                }
            }
        }

        /// <summary>
        /// Gets the original width for a filter grid column based on its index
        /// </summary>
        /// <param name="columnIndex">The column index</param>
        /// <returns>The original GridLength for the column</returns>
        private static GridLength GetOriginalColumnWidth(int columnIndex)
        {
            // Define original widths matching the XAML definition
            var originalWidths = new double[]
            {
                60,   // ID
                200,  // Name
                120,  // Retention time
                110,  // Precursor MZ
                120,  // Precursor type
                100,  // Ion mode
                120,  // CAS
                80,   // CID
                120,  // Formula
                100,  // Exact mass
                100,  // Cramer rules
                80,   // SVHC
                80,   // CMR
                100,  // CMR suspect
                80,   // EDC
                80,   // IARC
                100,  // EU SML
                100,  // China SML
                150,  // Carcinogenicity ISS
                150,  // DNA alerts OASIS
                150,  // DNA binding OASIS
                150,  // DNA binding OECD
                200,  // Protein binding alerts OASIS
                200,  // Vitro mutagenicity alerts ISS
                200,  // Vivo mutagenicity alerts ISS
                300,  // SMILES
                300,  // InChI
                300,  // InChIKey
                300,  // IUPAC name
                150,  // Superclass
                150,  // Class
                150,  // Subclass
                150,  // Ontology
                80,   // XLogP
                80,   // MLogP
                80,   // ALogP
                100,  // Topo PSA
                100,  // Num bonds
                100,  // Num atoms
                150,  // Authors
                150,  // Instrument
                120,  // Instrument type
                120,  // Collision energy
                100,  // Date
                100   // Num peaks
            };

            if (columnIndex >= 0 && columnIndex < originalWidths.Length)
            {
                return new GridLength(originalWidths[columnIndex]);
            }

            return new GridLength(100); // Default width
        }

        /// <summary>
        /// Applies both DataGrid column and filter control visibility in one call
        /// </summary>
        /// <param name="dataGrid">The DataGrid to modify</param>
        /// <param name="filterGrid">The Grid containing filter controls</param>
        /// <param name="visibleColumns">Set of column headers that should be visible</param>
        public static void ApplyCompleteVisibility(DataGrid dataGrid, Grid filterGrid, HashSet<string> visibleColumns)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Applying complete visibility for {visibleColumns.Count} columns");

                // Apply DataGrid column visibility first
                ApplyColumnVisibility(dataGrid, visibleColumns);

                // Then apply filter control visibility
                ApplyFilterControlVisibility(filterGrid, visibleColumns);

                System.Diagnostics.Debug.WriteLine("Complete visibility applied successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ApplyCompleteVisibility: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks if a property has meaningful data (not null, empty, zero, or default values)
        /// </summary>
        private static bool HasMeaningfulData(CompoundBean compound, string propertyName)
        {
            try
            {
                var property = typeof(CompoundBean).GetProperty(propertyName);
                if (property == null)
                    return false;

                var value = property.GetValue(compound);
                
                // Handle different data types
                switch (value)
                {
                    case null:
                        return false;
                    case string str:
                        return !string.IsNullOrWhiteSpace(str) && str != "/" && str != "-";
                    case int intVal:
                        return intVal != 0;
                    case float floatVal:
                        return floatVal != 0.0f && !float.IsNaN(floatVal);
                    case double doubleVal:
                        return doubleVal != 0.0 && !double.IsNaN(doubleVal);
                    default:
                        return true; // For other types, assume meaningful if not null
                }
            }
            catch
            {
                // If we can't access the property, assume it's not meaningful
                return false;
            }
        }
    }
}
