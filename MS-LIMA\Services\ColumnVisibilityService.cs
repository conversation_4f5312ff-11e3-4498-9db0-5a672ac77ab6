using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Metabolomics.MsLima.Bean;

namespace Metabolomics.MsLima.Services
{
    /// <summary>
    /// Service to manage dynamic column visibility based on available MSP data fields
    /// </summary>
    public static class ColumnVisibilityService
    {
        /// <summary>
        /// Mapping between CompoundBean property names and DataGrid column headers
        /// </summary>
        private static readonly Dictionary<string, string> PropertyToColumnHeaderMap = new Dictionary<string, string>
        {
            // Core identification fields
            { nameof(CompoundBean.Id), "ID" },
            { nameof(CompoundBean.Name), "Name" },
            { nameof(CompoundBean.RetentionTimes), "Retention time" },
            { nameof(CompoundBean.PrecursorMz), "Precursor MZ" },
            { nameof(CompoundBean.PrecursorType), "Precursor type" },
            { nameof(CompoundBean.IonMode), "Ion mode" },
            
            // Chemical identifiers
            { nameof(CompoundBean.CAS), "CAS" },
            { nameof(CompoundBean.CID), "CID" },
            { nameof(CompoundBean.Formula), "Formula" },
            { nameof(CompoundBean.ExactMass), "Exact mass" },
            
            // Toxicological/Regulatory fields
            { nameof(CompoundBean.Cramerrules), "Cramer rules" },
            { nameof(CompoundBean.SVHC), "SVHC" },
            { nameof(CompoundBean.CMR), "CMR" },
            { nameof(CompoundBean.CMRSuspect), "CMR suspect" },
            { nameof(CompoundBean.EDC), "EDC" },
            { nameof(CompoundBean.IARC), "IARC" },
            { nameof(CompoundBean.Eusml), "EU SML" },
            { nameof(CompoundBean.ChinaSml), "China SML" },
            { nameof(CompoundBean.Carcinogenicity_ISS), "Carcinogenicity ISS" },
            { nameof(CompoundBean.DNA_alerts_OASIS), "DNA alerts OASIS" },
            { nameof(CompoundBean.DNA_binding_OASIS), "DNA binding OASIS" },
            { nameof(CompoundBean.DNA_binding_OECD), "DNA binding OECD" },
            { nameof(CompoundBean.Protein_binding_alerts_OASIS), "Protein binding alerts OASIS" },
            { nameof(CompoundBean.Vitro_mutagenicity_alerts_ISS), "Vitro mutagenicity alerts ISS" },
            { nameof(CompoundBean.Vivo_mutagenicity_alerts_ISS), "Vivo mutagenicity alerts ISS" },
            
            // Chemical structure fields
            { nameof(CompoundBean.Smiles), "SMILES" },
            { nameof(CompoundBean.InChI), "InChI" },
            { nameof(CompoundBean.InChIKey), "InChIKey" },
            { nameof(CompoundBean.IUPACName), "IUPAC name" },
            
            // Classification fields
            { nameof(CompoundBean.Superclass), "Superclass" },
            { nameof(CompoundBean.Class), "Class" },
            { nameof(CompoundBean.Subclass), "Subclass" },
            { nameof(CompoundBean.Ontology), "Ontology" },
            
            // Physicochemical properties
            { nameof(CompoundBean.XLogP), "XLogP" },
            { nameof(CompoundBean.MLogP), "MLogP" },
            { nameof(CompoundBean.ALogP), "ALogP" },
            { nameof(CompoundBean.TopoPSA), "Topo PSA" },
            { nameof(CompoundBean.NumBond), "Num bonds" },
            { nameof(CompoundBean.NumAtom), "Num atoms" },
            
            // Metadata fields
            { nameof(CompoundBean.Authors), "Authors" },
            { nameof(CompoundBean.Instrument), "Instrument" },
            { nameof(CompoundBean.InstrumentType), "Instrument type" },
            { nameof(CompoundBean.CollisionEnergy), "Collision energy" },
            { nameof(CompoundBean.Date), "Date" },
            { nameof(CompoundBean.Num_peaks), "Num peaks" }
        };

        /// <summary>
        /// Core columns that should always be visible regardless of MSP content
        /// </summary>
        private static readonly HashSet<string> AlwaysVisibleColumns = new HashSet<string>
        {
            "ID",
            "Name"
        };

        /// <summary>
        /// Analyzes the first compound to determine which columns should be visible
        /// </summary>
        /// <param name="compounds">List of compounds from the loaded MSP file</param>
        /// <returns>Set of column headers that should be visible</returns>
        public static HashSet<string> DetermineVisibleColumns(List<CompoundBean> compounds)
        {
            var visibleColumns = new HashSet<string>(AlwaysVisibleColumns);
            
            if (compounds == null || compounds.Count == 0)
            {
                return visibleColumns;
            }

            // Analyze the first compound to determine available fields
            var firstCompound = compounds[0];
            
            foreach (var mapping in PropertyToColumnHeaderMap)
            {
                var propertyName = mapping.Key;
                var columnHeader = mapping.Value;
                
                // Skip already included core columns
                if (AlwaysVisibleColumns.Contains(columnHeader))
                    continue;
                
                // Check if the property has meaningful data
                if (HasMeaningfulData(firstCompound, propertyName))
                {
                    visibleColumns.Add(columnHeader);
                }
            }
            
            return visibleColumns;
        }

        /// <summary>
        /// Applies column visibility to a DataGrid based on visible columns set
        /// </summary>
        /// <param name="dataGrid">The DataGrid to modify</param>
        /// <param name="visibleColumns">Set of column headers that should be visible</param>
        public static void ApplyColumnVisibility(DataGrid dataGrid, HashSet<string> visibleColumns)
        {
            if (dataGrid?.Columns == null)
                return;

            foreach (DataGridColumn column in dataGrid.Columns)
            {
                var header = column.Header?.ToString();
                if (!string.IsNullOrEmpty(header))
                {
                    column.Visibility = visibleColumns.Contains(header)
                        ? System.Windows.Visibility.Visible
                        : System.Windows.Visibility.Collapsed;
                }
            }
        }

        /// <summary>
        /// Applies filter control visibility to match the visible columns
        /// </summary>
        /// <param name="filterGrid">The Grid containing filter controls</param>
        /// <param name="visibleColumns">Set of column headers that should be visible</param>
        public static void ApplyFilterControlVisibility(Grid filterGrid, HashSet<string> visibleColumns)
        {
            if (filterGrid?.Children == null)
                return;

            foreach (UIElement child in filterGrid.Children)
            {
                if (child is TextBox textBox)
                {
                    var tag = textBox.Tag?.ToString();
                    if (!string.IsNullOrEmpty(tag))
                    {
                        textBox.Visibility = visibleColumns.Contains(tag)
                            ? System.Windows.Visibility.Visible
                            : System.Windows.Visibility.Collapsed;
                    }
                }
            }
        }

        /// <summary>
        /// Applies both DataGrid column and filter control visibility in one call
        /// </summary>
        /// <param name="dataGrid">The DataGrid to modify</param>
        /// <param name="filterGrid">The Grid containing filter controls</param>
        /// <param name="visibleColumns">Set of column headers that should be visible</param>
        public static void ApplyCompleteVisibility(DataGrid dataGrid, Grid filterGrid, HashSet<string> visibleColumns)
        {
            ApplyColumnVisibility(dataGrid, visibleColumns);
            ApplyFilterControlVisibility(filterGrid, visibleColumns);
        }

        /// <summary>
        /// Checks if a property has meaningful data (not null, empty, zero, or default values)
        /// </summary>
        private static bool HasMeaningfulData(CompoundBean compound, string propertyName)
        {
            try
            {
                var property = typeof(CompoundBean).GetProperty(propertyName);
                if (property == null)
                    return false;

                var value = property.GetValue(compound);
                
                // Handle different data types
                switch (value)
                {
                    case null:
                        return false;
                    case string str:
                        return !string.IsNullOrWhiteSpace(str) && str != "/" && str != "-";
                    case int intVal:
                        return intVal != 0;
                    case float floatVal:
                        return floatVal != 0.0f && !float.IsNaN(floatVal);
                    case double doubleVal:
                        return doubleVal != 0.0 && !double.IsNaN(doubleVal);
                    default:
                        return true; // For other types, assume meaningful if not null
                }
            }
            catch
            {
                // If we can't access the property, assume it's not meaningful
                return false;
            }
        }
    }
}
