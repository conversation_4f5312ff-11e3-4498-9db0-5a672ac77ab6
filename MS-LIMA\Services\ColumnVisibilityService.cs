using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Metabolomics.MsLima.Bean;

namespace Metabolomics.MsLima.Services
{
    /// <summary>
    /// Service to manage dynamic column visibility based on available MSP data fields
    /// </summary>
    public static class ColumnVisibilityService
    {
        /// <summary>
        /// Mapping between CompoundBean property names and DataGrid column headers
        /// </summary>
        private static readonly Dictionary<string, string> PropertyToColumnHeaderMap = new Dictionary<string, string>
        {
            // Core identification fields
            { nameof(CompoundBean.Id), "ID" },
            { nameof(CompoundBean.Name), "Name" },
            { nameof(CompoundBean.RetentionTimes), "Retention time" },
            { nameof(CompoundBean.PrecursorMz), "Precursor MZ" },
            { nameof(CompoundBean.PrecursorType), "Precursor type" },
            { nameof(CompoundBean.IonMode), "Ion mode" },
            
            // Chemical identifiers
            { nameof(CompoundBean.CAS), "CAS" },
            { nameof(CompoundBean.CID), "CID" },
            { nameof(CompoundBean.Formula), "Formula" },
            { nameof(CompoundBean.ExactMass), "Exact mass" },
            
            // Toxicological/Regulatory fields
            { nameof(CompoundBean.Cramerrules), "Cramer rules" },
            { nameof(CompoundBean.SVHC), "SVHC" },
            { nameof(CompoundBean.CMR), "CMR" },
            { nameof(CompoundBean.CMRSuspect), "CMR suspect" },
            { nameof(CompoundBean.EDC), "EDC" },
            { nameof(CompoundBean.IARC), "IARC" },
            { nameof(CompoundBean.Eusml), "EU SML" },
            { nameof(CompoundBean.ChinaSml), "China SML" },
            { nameof(CompoundBean.Carcinogenicity_ISS), "Carcinogenicity ISS" },
            { nameof(CompoundBean.DNA_alerts_OASIS), "DNA alerts OASIS" },
            { nameof(CompoundBean.DNA_binding_OASIS), "DNA binding OASIS" },
            { nameof(CompoundBean.DNA_binding_OECD), "DNA binding OECD" },
            { nameof(CompoundBean.Protein_binding_alerts_OASIS), "Protein binding alerts OASIS" },
            { nameof(CompoundBean.Vitro_mutagenicity_alerts_ISS), "Vitro mutagenicity alerts ISS" },
            { nameof(CompoundBean.Vivo_mutagenicity_alerts_ISS), "Vivo mutagenicity alerts ISS" },
            
            // Chemical structure fields
            { nameof(CompoundBean.Smiles), "SMILES" },
            { nameof(CompoundBean.InChI), "InChI" },
            { nameof(CompoundBean.InChIKey), "InChIKey" },
            { nameof(CompoundBean.IUPACName), "IUPAC name" },
            
            // Classification fields
            { nameof(CompoundBean.Superclass), "Superclass" },
            { nameof(CompoundBean.Class), "Class" },
            { nameof(CompoundBean.Subclass), "Subclass" },
            { nameof(CompoundBean.Ontology), "Ontology" },
            
            // Physicochemical properties
            { nameof(CompoundBean.XLogP), "XLogP" },
            { nameof(CompoundBean.MLogP), "MLogP" },
            { nameof(CompoundBean.ALogP), "ALogP" },
            { nameof(CompoundBean.TopoPSA), "Topo PSA" },
            { nameof(CompoundBean.NumBond), "Num bonds" },
            { nameof(CompoundBean.NumAtom), "Num atoms" },
            
            // Metadata fields
            { nameof(CompoundBean.Authors), "Authors" },
            { nameof(CompoundBean.Instrument), "Instrument" },
            { nameof(CompoundBean.InstrumentType), "Instrument type" },
            { nameof(CompoundBean.CollisionEnergy), "Collision energy" },
            { nameof(CompoundBean.Date), "Date" },
            { nameof(CompoundBean.Num_peaks), "Num peaks" }
        };

        /// <summary>
        /// Core columns that should always be visible regardless of MSP content
        /// </summary>
        private static readonly HashSet<string> AlwaysVisibleColumns = new HashSet<string>
        {
            "ID",
            "Name"
        };

        /// <summary>
        /// Analyzes the first compound to determine which columns should be visible
        /// </summary>
        /// <param name="compounds">List of compounds from the loaded MSP file</param>
        /// <returns>Set of column headers that should be visible</returns>
        public static HashSet<string> DetermineVisibleColumns(List<CompoundBean> compounds)
        {
            var visibleColumns = new HashSet<string>(AlwaysVisibleColumns);
            
            if (compounds == null || compounds.Count == 0)
            {
                return visibleColumns;
            }

            // Analyze the first compound to determine available fields
            var firstCompound = compounds[0];
            
            foreach (var mapping in PropertyToColumnHeaderMap)
            {
                var propertyName = mapping.Key;
                var columnHeader = mapping.Value;
                
                // Skip already included core columns
                if (AlwaysVisibleColumns.Contains(columnHeader))
                    continue;
                
                // Check if the property has meaningful data
                if (HasMeaningfulData(firstCompound, propertyName))
                {
                    visibleColumns.Add(columnHeader);
                }
            }
            
            return visibleColumns;
        }

        /// <summary>
        /// Applies column visibility to a DataGrid based on visible columns set
        /// </summary>
        /// <param name="dataGrid">The DataGrid to modify</param>
        /// <param name="visibleColumns">Set of column headers that should be visible</param>
        public static void ApplyColumnVisibility(DataGrid dataGrid, HashSet<string> visibleColumns)
        {
            if (dataGrid?.Columns == null)
                return;

            foreach (DataGridColumn column in dataGrid.Columns)
            {
                var header = column.Header?.ToString();
                if (!string.IsNullOrEmpty(header))
                {
                    column.Visibility = visibleColumns.Contains(header)
                        ? System.Windows.Visibility.Visible
                        : System.Windows.Visibility.Collapsed;
                }
            }
        }

        /// <summary>
        /// Applies filter control visibility to match the visible columns with perfect alignment
        /// This method preserves the original filter structure and only modifies visibility and positioning
        /// </summary>
        /// <param name="filterGrid">The Grid containing filter controls</param>
        /// <param name="dataGrid">The DataGrid to synchronize with</param>
        /// <param name="visibleColumns">Set of column headers that should be visible</param>
        public static void ApplyFilterControlVisibility(Grid filterGrid, DataGrid dataGrid, HashSet<string> visibleColumns)
        {
            if (filterGrid?.Children == null || filterGrid.ColumnDefinitions == null || dataGrid?.Columns == null)
                return;

            try
            {
                System.Diagnostics.Debug.WriteLine($"Applying filter visibility for {visibleColumns.Count} visible columns");

                // Create mapping of column headers to their DataGrid columns for width synchronization
                var columnMapping = new Dictionary<string, DataGridColumn>();
                foreach (DataGridColumn column in dataGrid.Columns)
                {
                    var header = column.Header?.ToString();
                    if (!string.IsNullOrEmpty(header))
                    {
                        columnMapping[header] = column;
                    }
                }

                // IMPROVED APPROACH: Preserve original structure and only modify visibility
                // First, hide all filter controls and column definitions
                foreach (UIElement child in filterGrid.Children)
                {
                    if (child is TextBox textBox)
                    {
                        textBox.Visibility = System.Windows.Visibility.Collapsed;
                    }
                }

                // Hide all column definitions by setting width to 0
                for (int i = 0; i < filterGrid.ColumnDefinitions.Count; i++)
                {
                    filterGrid.ColumnDefinitions[i].Width = new GridLength(0);
                }

                // Now show only the filters for visible columns and set their widths
                foreach (UIElement child in filterGrid.Children)
                {
                    if (child is TextBox textBox)
                    {
                        var tag = textBox.Tag?.ToString();
                        if (!string.IsNullOrEmpty(tag) && visibleColumns.Contains(tag))
                        {
                            // Show the filter control
                            textBox.Visibility = System.Windows.Visibility.Visible;

                            // Get the column index for this filter
                            int columnIndex = Grid.GetColumn(textBox);

                            // Set the column definition width
                            if (columnIndex >= 0 && columnIndex < filterGrid.ColumnDefinitions.Count)
                            {
                                var colDef = filterGrid.ColumnDefinitions[columnIndex];

                                // Try to get width from corresponding DataGrid column first
                                if (columnMapping.TryGetValue(tag, out var dataGridColumn))
                                {
                                    // Convert DataGridLength to GridLength
                                    if (dataGridColumn.Width.IsAbsolute)
                                    {
                                        colDef.Width = new GridLength(dataGridColumn.Width.Value);
                                    }
                                    else if (dataGridColumn.Width.IsStar)
                                    {
                                        colDef.Width = new GridLength(dataGridColumn.Width.Value, GridUnitType.Star);
                                    }
                                    else
                                    {
                                        // Auto or other types - use original width
                                        colDef.Width = GetOriginalColumnWidth(columnIndex);
                                    }
                                    System.Diagnostics.Debug.WriteLine($"Filter column '{tag}' width synchronized with DataGrid: {dataGridColumn.Width}");
                                }
                                else
                                {
                                    // Fallback to original width
                                    colDef.Width = GetOriginalColumnWidth(columnIndex);
                                    System.Diagnostics.Debug.WriteLine($"Filter column '{tag}' using original width: {GetOriginalColumnWidth(columnIndex)}");
                                }
                            }
                        }
                    }
                }

                // Count visible filters for debugging
                int visibleFilterCount = filterGrid.Children.OfType<TextBox>()
                    .Count(tb => tb.Visibility == System.Windows.Visibility.Visible);

                System.Diagnostics.Debug.WriteLine($"Filter visibility applied: {visibleFilterCount} filters visible out of {filterGrid.Children.Count} total");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ApplyFilterControlVisibility: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the original width for a filter grid column based on its index
        /// </summary>
        /// <param name="columnIndex">The column index</param>
        /// <returns>The original GridLength for the column</returns>
        private static GridLength GetOriginalColumnWidth(int columnIndex)
        {
            // Define original widths matching the XAML definition
            var originalWidths = new double[]
            {
                60,   // ID
                200,  // Name
                120,  // Retention time
                110,  // Precursor MZ
                120,  // Precursor type
                100,  // Ion mode
                120,  // CAS
                80,   // CID
                120,  // Formula
                100,  // Exact mass
                100,  // Cramer rules
                80,   // SVHC
                80,   // CMR
                100,  // CMR suspect
                80,   // EDC
                80,   // IARC
                100,  // EU SML
                100,  // China SML
                150,  // Carcinogenicity ISS
                150,  // DNA alerts OASIS
                150,  // DNA binding OASIS
                150,  // DNA binding OECD
                200,  // Protein binding alerts OASIS
                200,  // Vitro mutagenicity alerts ISS
                200,  // Vivo mutagenicity alerts ISS
                300,  // SMILES
                300,  // InChI
                300,  // InChIKey
                300,  // IUPAC name
                150,  // Superclass
                150,  // Class
                150,  // Subclass
                150,  // Ontology
                80,   // XLogP
                80,   // MLogP
                80,   // ALogP
                100,  // Topo PSA
                100,  // Num bonds
                100,  // Num atoms
                150,  // Authors
                150,  // Instrument
                120,  // Instrument type
                120,  // Collision energy
                100,  // Date
                100   // Num peaks
            };

            if (columnIndex >= 0 && columnIndex < originalWidths.Length)
            {
                return new GridLength(originalWidths[columnIndex]);
            }

            return new GridLength(100); // Default width
        }

        /// <summary>
        /// Synchronizes filter control widths with their corresponding DataGrid columns
        /// Works with the preserved filter structure approach
        /// </summary>
        /// <param name="filterGrid">The Grid containing filter controls</param>
        /// <param name="dataGrid">The DataGrid to synchronize with</param>
        public static void SynchronizeFilterWidths(Grid filterGrid, DataGrid dataGrid)
        {
            if (filterGrid?.Children == null || filterGrid.ColumnDefinitions == null || dataGrid?.Columns == null)
                return;

            try
            {
                // Create mapping of visible DataGrid columns by header
                var columnMapping = new Dictionary<string, DataGridColumn>();
                foreach (DataGridColumn column in dataGrid.Columns)
                {
                    var header = column.Header?.ToString();
                    if (!string.IsNullOrEmpty(header) && column.Visibility == System.Windows.Visibility.Visible)
                    {
                        columnMapping[header] = column;
                    }
                }

                // Update filter column widths for visible filters only
                foreach (UIElement child in filterGrid.Children)
                {
                    if (child is TextBox textBox && textBox.Visibility == System.Windows.Visibility.Visible)
                    {
                        var tag = textBox.Tag?.ToString();
                        if (!string.IsNullOrEmpty(tag) && columnMapping.TryGetValue(tag, out var dataGridColumn))
                        {
                            // Get the column index for this filter
                            int columnIndex = Grid.GetColumn(textBox);

                            if (columnIndex >= 0 && columnIndex < filterGrid.ColumnDefinitions.Count)
                            {
                                var colDef = filterGrid.ColumnDefinitions[columnIndex];

                                // Convert DataGridLength to GridLength
                                if (dataGridColumn.Width.IsAbsolute)
                                {
                                    colDef.Width = new GridLength(dataGridColumn.Width.Value);
                                }
                                else if (dataGridColumn.Width.IsStar)
                                {
                                    colDef.Width = new GridLength(dataGridColumn.Width.Value, GridUnitType.Star);
                                }
                                else
                                {
                                    // Auto or other types - use original width
                                    colDef.Width = GetOriginalColumnWidth(columnIndex);
                                }
                                System.Diagnostics.Debug.WriteLine($"Synchronized filter width for '{tag}' at column {columnIndex}: {dataGridColumn.Width}");
                            }
                        }
                    }
                }

                // Count synchronized filters for debugging
                int synchronizedCount = filterGrid.Children.OfType<TextBox>()
                    .Count(tb => tb.Visibility == System.Windows.Visibility.Visible);

                System.Diagnostics.Debug.WriteLine($"Filter width synchronization completed: {synchronizedCount} filters synchronized");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in SynchronizeFilterWidths: {ex.Message}");
            }
        }

        /// <summary>
        /// Verifies that filter controls are perfectly aligned with their corresponding DataGrid columns
        /// </summary>
        /// <param name="filterGrid">The Grid containing filter controls</param>
        /// <param name="dataGrid">The DataGrid to verify alignment with</param>
        /// <returns>True if alignment is perfect, false otherwise</returns>
        public static bool VerifyFilterColumnAlignment(Grid filterGrid, DataGrid dataGrid)
        {
            if (filterGrid?.Children == null || dataGrid?.Columns == null)
                return false;

            try
            {
                int alignmentIssues = 0;

                foreach (UIElement child in filterGrid.Children)
                {
                    if (child is TextBox textBox && textBox.Visibility == System.Windows.Visibility.Visible)
                    {
                        var tag = textBox.Tag?.ToString();
                        if (!string.IsNullOrEmpty(tag))
                        {
                            // Find corresponding DataGrid column
                            var dataGridColumn = dataGrid.Columns.FirstOrDefault(c => c.Header?.ToString() == tag);
                            if (dataGridColumn != null && dataGridColumn.Visibility == System.Windows.Visibility.Visible)
                            {
                                // Check if filter is positioned correctly
                                int filterColumnIndex = Grid.GetColumn(textBox);

                                // Verify width alignment
                                if (filterColumnIndex >= 0 && filterColumnIndex < filterGrid.ColumnDefinitions.Count)
                                {
                                    var filterColDef = filterGrid.ColumnDefinitions[filterColumnIndex];

                                    // Compare widths (allowing for small differences due to conversion)
                                    double filterWidth = filterColDef.Width.Value;
                                    double dataGridWidth = dataGridColumn.Width.IsAbsolute ? dataGridColumn.Width.Value : 0;

                                    if (dataGridColumn.Width.IsAbsolute && Math.Abs(filterWidth - dataGridWidth) > 1.0)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"Width mismatch for '{tag}': Filter={filterWidth:F1}, DataGrid={dataGridWidth:F1}");
                                        alignmentIssues++;
                                    }
                                }
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"Visible filter '{tag}' has no corresponding visible DataGrid column");
                                alignmentIssues++;
                            }
                        }
                    }
                }

                bool isAligned = alignmentIssues == 0;
                System.Diagnostics.Debug.WriteLine($"Filter-column alignment verification: {(isAligned ? "PERFECT" : $"{alignmentIssues} issues found")}");

                return isAligned;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error verifying filter-column alignment: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Applies both DataGrid column and filter control visibility with perfect synchronization
        /// </summary>
        /// <param name="dataGrid">The DataGrid to modify</param>
        /// <param name="filterGrid">The Grid containing filter controls</param>
        /// <param name="visibleColumns">Set of column headers that should be visible</param>
        public static void ApplyCompleteVisibility(DataGrid dataGrid, Grid filterGrid, HashSet<string> visibleColumns)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Applying complete visibility for {visibleColumns.Count} columns");

                // Apply DataGrid column visibility first
                ApplyColumnVisibility(dataGrid, visibleColumns);

                // Then apply filter control visibility with width synchronization
                ApplyFilterControlVisibility(filterGrid, dataGrid, visibleColumns);

                // Verify alignment after applying visibility
                VerifyFilterColumnAlignment(filterGrid, dataGrid);

                System.Diagnostics.Debug.WriteLine("Complete visibility applied successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ApplyCompleteVisibility: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks if a property has meaningful data (not null, empty, zero, or default values)
        /// </summary>
        private static bool HasMeaningfulData(CompoundBean compound, string propertyName)
        {
            try
            {
                var property = typeof(CompoundBean).GetProperty(propertyName);
                if (property == null)
                    return false;

                var value = property.GetValue(compound);
                
                // Handle different data types
                switch (value)
                {
                    case null:
                        return false;
                    case string str:
                        return !string.IsNullOrWhiteSpace(str) && str != "/" && str != "-";
                    case int intVal:
                        return intVal != 0;
                    case float floatVal:
                        return floatVal != 0.0f && !float.IsNaN(floatVal);
                    case double doubleVal:
                        return doubleVal != 0.0 && !double.IsNaN(doubleVal);
                    default:
                        return true; // For other types, assume meaningful if not null
                }
            }
            catch
            {
                // If we can't access the property, assume it's not meaningful
                return false;
            }
        }
    }
}
