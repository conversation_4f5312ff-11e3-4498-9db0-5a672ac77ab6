﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Windows;
using System.Windows.Threading;
using System.Windows.Input;
using Microsoft.Win32;
using Metabolomics.Core;
using Metabolomics.MsLima.Model;
using Metabolomics.MsLima.Bean;
using System.Windows.Controls;
using ChartDrawing;

namespace Metabolomics.MsLima
{
    public class MainWindowVM : ViewModelBase
    {
        #region properties

        public MsLimaData MsLimaData { get; set; }
        public MassSpectrumViewHandler MsHandler { get; set; }
        public ControlRefresh ControlRefresh { get; set; }
        public AutoRepeater AutoExporter { get; set; }

        public List<CompoundBean> CompoundTable {
            get => MsLimaData.DataStorage.CompoundList;
        }
        public FilteredTable FilteredCompoundTable { get; set; }

        public FilterSettingsForLibrary FilteredTableSetting { get; set; }
        public TabMassSpectraView TabMassSpectraView { get; set; }
        public TabMassSpectrumTable TabMassSpectrumTable { get; set; } = TabMassSpectrumTable.SinglePeak;

        public ICollectionView FilteredCompoundTableView
        {
            get
            {
                if (FilteredCompoundTable == null) return null;
                return FilteredCompoundTable.View;
            }
        }

        public List<MsGroup> ConsensusSpectraTable {
            get {
                if (SelectedCompoundBean == null) return null;
                return MsGrouping.Excute(SelectedCompoundBean);
            }
        }

        // Private fields for update flags
        private bool _shouldUpdateSingleMassSpectrumVM = true;
        private bool _shouldUpdateMultipleSpectrumVM = true;
        private bool _shouldUpdateConsensusSpectrumVM = true;
        private bool _structureErrorShown = false; // Track if structure error message has been shown

        // Public properties for performance optimization access from MainWindow
        /// <summary>
        /// Gets or sets whether the single mass spectrum view model should be updated.
        /// Used by performance optimization in MainWindow for selective refresh.
        /// </summary>
        public bool ShouldUpdateSingleMassSpectrumVM
        {
            get => _shouldUpdateSingleMassSpectrumVM;
            set => _shouldUpdateSingleMassSpectrumVM = value;
        }

        /// <summary>
        /// Gets or sets whether the multiple spectrum view model should be updated.
        /// Used by performance optimization in MainWindow for selective refresh.
        /// </summary>
        public bool ShouldUpdateMultipleSpectrumVM
        {
            get => _shouldUpdateMultipleSpectrumVM;
            set => _shouldUpdateMultipleSpectrumVM = value;
        }

        /// <summary>
        /// Gets or sets whether the consensus spectrum view model should be updated.
        /// Used by performance optimization in MainWindow for selective refresh.
        /// </summary>
        public bool ShouldUpdateConsensusSpectrumVM
        {
            get => _shouldUpdateConsensusSpectrumVM;
            set => _shouldUpdateConsensusSpectrumVM = value;
        }

        #region Selected Items
        private AnnotatedPeak selectedPeak;
        private MassSpectrum selectedSpectrum;
        private CompoundBean selectedCompoundBean;
        private MsGroup selectedMsGroup;
        public AnnotatedPeak SelectedPeak {
            get => selectedPeak;
            set => OnPropertyChangedIfSet(ref selectedPeak, value, nameof(SelectedPeak));
        }

        public MassSpectrum SelectedSpectrum {
            get => selectedSpectrum;
            set {
                selectedSpectrum = value;
                OnPropertyChanged(nameof(SelectedSpectrum));
                SelectedSpectrumChanged();
            }
        }

        public CompoundBean SelectedCompoundBean {
            get => selectedCompoundBean;
            set {
                OnPropertyChangedIfSet(ref selectedCompoundBean, value, nameof(SelectedCompoundBean));
                SelectedCompoundChanged();
            }
        }

        public MsGroup SelectedMsGroup {
            get => selectedMsGroup;
            set => OnPropertyChangedIfSet(ref selectedMsGroup, value, nameof(SelectedMsGroup));
        }
        #endregion

        #region Label
        private string mainWindowTitle = Properties.Resources.Version;
        public string MainWindowTitle {
            get => mainWindowTitle;
            set => OnPropertyChangedIfSet(ref mainWindowTitle, value, nameof(MainWindowTitle));
        }
        public string LabelNumCompounds {
            get {
                if (CompoundTable == null) return ""; return "Number of compounds: " + CompoundTable.Count;
            }
        }
        public string LabelNumSpectra {
            get {
                if (MsLimaData.DataStorage.RawLibraryFile == null) return "Please import a library"; return "Number of spectra: " + MsLimaData.DataStorage.RawLibraryFile.Count;
            }
        }
        public string LabelSelectedCompound {
            get {
                if (SelectedCompoundBean == null) return ""; return "Selected compound: " + SelectedCompoundBean.Name;
            }
        }
        public string LabelSelectedSpectra {
            get {
                if (SelectedSpectrum == null) return ""; return "Spectra ID: " + SelectedSpectrum.Id + ", " + SelectedSpectrum.AdductIon.AdductIonName + ", " + SelectedSpectrum.CollisionEnergy + "eV";
            }
        }

        private System.Windows.Media.Imaging.BitmapImage structureImage;

        public System.Windows.Media.Imaging.BitmapImage StructureImage {
            get => structureImage;
            set => OnPropertyChangedIfSet(ref structureImage, value, nameof(StructureImage)); }

        #region Progress Bar Properties
        private bool isImportInProgress = false;
        public bool IsImportInProgress {
            get => isImportInProgress;
            set => OnPropertyChangedIfSet(ref isImportInProgress, value, nameof(IsImportInProgress));
        }

        private double importProgress = 0;
        public double ImportProgress {
            get => importProgress;
            set => OnPropertyChangedIfSet(ref importProgress, value, nameof(ImportProgress));
        }

        private bool isImportIndeterminate = false;
        public bool IsImportIndeterminate {
            get => isImportIndeterminate;
            set => OnPropertyChangedIfSet(ref isImportIndeterminate, value, nameof(IsImportIndeterminate));
        }
        #endregion
        #endregion

        #region ViewModel
        private DrawVisualMassSpectrum singleMassSpectrumVM = new DrawVisualMassSpectrum();
        public DrawVisualMassSpectrum SingleMassSpectrumVM {
            get => singleMassSpectrumVM;
            set {
                singleMassSpectrumVM = value;
                OnPropertyChanged(nameof(SingleMassSpectrumVM));
                SingleMassSpectrumUI.UpdateFE(SingleMassSpectrumVM);
            }
        }

        private DrawVisualMassSpectrum consensusSpectrumVM = new DrawVisualMassSpectrum();
        public DrawVisualMassSpectrum ConsensusSpectrumVM {
            get => consensusSpectrumVM;
            set {
                consensusSpectrumVM = value;
                OnPropertyChanged(nameof(ConsensusSpectrumVM));
                ConsensusSpectrumUI.UpdateFE(ConsensusSpectrumVM);
            }
        }
        #endregion

        #region UI
        private MassSpectrumUI singleMassSpectrumUI;
        public MassSpectrumUI SingleMassSpectrumUI {
            get => singleMassSpectrumUI;
            set => OnPropertyChangedIfSet(ref singleMassSpectrumUI, value, nameof(SingleMassSpectrumUI));
        }

        private MassSpectrumUI consensusSpectrumUI;
        public MassSpectrumUI ConsensusSpectrumUI {
            get => consensusSpectrumUI;
            set => OnPropertyChangedIfSet(ref consensusSpectrumUI, value, nameof(ConsensusSpectrumUI));
        }

        private StackPanel multipleSpectra;
        public StackPanel MultipleSpectra {
            get => multipleSpectra;
            set => OnPropertyChangedIfSet(ref multipleSpectra, value, nameof(MultipleSpectra));
        }

        #endregion

        #region Filter

        // Dictionary to store column filter values
        private Dictionary<string, string> columnFilterValues = new Dictionary<string, string>();

        // Method to get a column filter value
        public string GetColumnFilterValue(string columnName)
        {
            if (columnFilterValues.ContainsKey(columnName))
                return columnFilterValues[columnName];
            return string.Empty;
        }

        // Method to set a column filter value
        public void SetColumnFilterValue(string columnName, string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                if (columnFilterValues.ContainsKey(columnName))
                    columnFilterValues.Remove(columnName);
            }
            else
            {
                if (columnFilterValues.ContainsKey(columnName))
                    columnFilterValues[columnName] = value;
                else
                    columnFilterValues.Add(columnName, value);
            }

            // Update the filter in FilteredTableSetting
            if (FilteredTableSetting != null)
                FilteredTableSetting.SetColumnFilter(columnName, value);

            // FIXED: Notify indexer property correctly for WPF binding
            // WPF indexer binding requires "Item[]" format for notifications
            OnPropertyChanged("Item[]"); // Notify all indexer properties
            OnPropertyChanged($"Item[{columnName}]"); // Notify specific indexer property
            OnPropertyChanged($"ColumnFilter_{columnName}"); // Legacy compatibility
        }

        // Legacy filter properties (kept for backward compatibility)
        public string FilterNameText {
            get => GetColumnFilterValue("Name");
            set {
                SetColumnFilterValue("Name", value);
                OnPropertyChanged(nameof(FilterNameText));
            }
        }

        public string FilterMzText {
            get => GetColumnFilterValue("MW");
            set {
                SetColumnFilterValue("MW", value);
                OnPropertyChanged(nameof(FilterMzText));
            }
        }

        public string FilterRtText {
            get => GetColumnFilterValue("RTs");
            set {
                SetColumnFilterValue("RTs", value);
                OnPropertyChanged(nameof(FilterRtText));
            }
        }

        public string FilterInChIKeyText {
            get => GetColumnFilterValue("InChIKey");
            set {
                SetColumnFilterValue("InChIKey", value);
                OnPropertyChanged(nameof(FilterInChIKeyText));
            }
        }

        // Dynamic property accessor for column filters - FIXED: Added 'new' keyword to properly hide inherited member
        public new string this[string columnName]
        {
            get {
                var value = GetColumnFilterValue(columnName);
                Debug.WriteLine($"Indexer GET [{columnName}] = '{value}'");
                return value;
            }
            set {
                Debug.WriteLine($"Indexer SET [{columnName}] = '{value}'");
                SetColumnFilterValue(columnName, value);
            }
        }

        /// <summary>
        /// ULTRA-ROBUST synchronous clear all filters - eliminates all crash scenarios
        /// </summary>
        public void ClearAllFilters()
        {
            try
            {
                Debug.WriteLine("ClearAllFilters: Starting ROBUST filter clear operation");

                // SAFETY CHECK 1: Ensure we're on the UI thread
                if (!Application.Current.Dispatcher.CheckAccess())
                {
                    Debug.WriteLine("ClearAllFilters: Not on UI thread, dispatching to UI thread");
                    Application.Current.Dispatcher.Invoke(() => ClearAllFilters());
                    return;
                }

                // SAFETY CHECK 2: Clear local dictionary safely
                if (columnFilterValues != null)
                {
                    columnFilterValues.Clear();
                    Debug.WriteLine("ClearAllFilters: Cleared columnFilterValues dictionary");
                }

                // SAFETY CHECK 3: Clear FilteredTableSetting filters safely
                if (FilteredTableSetting != null)
                {
                    try
                    {
                        FilteredTableSetting.ClearAllFilters();
                        Debug.WriteLine("ClearAllFilters: Cleared FilteredTableSetting filters");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"ClearAllFilters: Error clearing FilteredTableSetting: {ex.Message}");
                        // Continue with other clearing operations
                    }
                }

                // COMPREHENSIVE: Clear all filter columns
                var filterColumns = new[] {
                    "Name", "CAS", "CID", "PrecursorType", "CollisionEnergy",
                    "Cramerrules", "SVHC", "CMR", "CMRSuspect", "EDC",
                    "IARC", "Eusml", "ChinaSml"
                };

                // SAFETY CHECK 4: Clear each filter individually with error handling
                foreach (var column in filterColumns)
                {
                    try
                    {
                        if (columnFilterValues != null && columnFilterValues.ContainsKey(column))
                        {
                            columnFilterValues[column] = "";
                        }
                        else if (columnFilterValues != null)
                        {
                            columnFilterValues[column] = "";
                        }
                        Debug.WriteLine($"ClearAllFilters: Cleared filter for {column}");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"ClearAllFilters: Error clearing filter {column}: {ex.Message}");
                        // Continue with next filter
                    }
                }

                // SAFETY CHECK 5: Notify property changes safely
                try
                {
                    // Notify all indexer properties at once
                    OnPropertyChanged("Item[]");
                    Debug.WriteLine("ClearAllFilters: Notified all indexer properties with Item[]");

                    // Notify specific indexer properties
                    foreach (var column in filterColumns)
                    {
                        OnPropertyChanged($"Item[{column}]");
                        Debug.WriteLine($"ClearAllFilters: Notified property change for {column}");
                    }

                    // Notify legacy filter properties
                    OnPropertyChanged(nameof(FilterNameText));
                    OnPropertyChanged(nameof(FilterMzText));
                    OnPropertyChanged(nameof(FilterRtText));
                    OnPropertyChanged(nameof(FilterInChIKeyText));
                    Debug.WriteLine("ClearAllFilters: Notified legacy filter properties");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"ClearAllFilters: Error notifying property changes: {ex.Message}");
                    // Continue - property notifications are not critical for functionality
                }

                Debug.WriteLine("ClearAllFilters: ROBUST filter clear operation completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"CRITICAL ERROR in ClearAllFilters: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // ULTIMATE FALLBACK: Show error message but don't crash
                try
                {
                    MessageBox.Show($"Error clearing filters: {ex.Message}\n\nThe application will continue to work normally.",
                                  "Filter Clear Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
                catch
                {
                    // Even MessageBox failed - just log and continue
                    Debug.WriteLine("CRITICAL: Even error message display failed");
                }
            }
        }
        #endregion


        #region Command

        #region Selection Changed Commands
        public DelegateCommand SelectionChangedSingleSpectrumTableCommand { get; set; }
        public DelegateCommand SelectionChangedConsensusTableCommand { get; set; }

        //public DelegateCommand SelectionChangedTabControlMsViewCommand { get; set; }
        public DelegateCommand SelectionChangedTabControlMsTableCommand { get; set; }


        #endregion
        #region Menu Commands
        public DelegateCommand ImportFileCommand { get; set; }
        public DelegateCommand ImportMassBankFileCommand { get; set; }

        public DelegateCommand SaveAsMspCommand { get; set; }
        public DelegateCommand SaveAsMspWithoutRTCommand { get; set; }
        public DelegateCommand SaveAsMgfCommand { get; set; }
        public DelegateCommand SaveAsMzMineCommand { get; set; }

        public DelegateCommand WindowLoaded { get; set; }

        public DelegateCommand StartUpSettingWindow { get; set; }

        public DelegateCommand StartUpWindowAllSpectra { get; set; }

        public DelegateCommand StartUpWindowComparativeViewer { get; set; }

        public DelegateCommand ConvertAccurateMassToTheoreticalMass { get; set; }
        public DelegateCommand DropRetentionTime { get; set; }
        public DelegateCommand RemoveUnannotatedCommand { get; set; }
        public DelegateCommand SaveCommonProductIonCommand { get; set; }
        public DelegateCommand UpdateSmilesAndInChiBasedOnInChIKeyCommand { get; set; }
        public DelegateCommand UpdateCommonMetaDataCommand { get; set; }
        public DelegateCommand CheckRtDifferencesCommand { get; set; }
        public DelegateCommand CheckInChIKeyDifferencesCommand { get; set; }
        public DelegateCommand CheckFormulaDifferencesCommand { get; set; }
        public DelegateCommand CheckAllValidationCommand { get; set; }

        // Filter Commands
        public DelegateCommand ClearAllFiltersCommand { get; set; }
        #endregion


        // Change comments to add particular sentence;
        private DelegateCommand temporaryMethod;
        public DelegateCommand TemporaryMethods {
            get {
                return temporaryMethod ?? new DelegateCommand(x =>
                {
                    if (this.CompoundTable == null) return;
                    foreach (var c in CompoundTable)
                    {
                        foreach (var spec in c.Spectra)
                        {
                            if (spec.Comment.Contains("CorrelDec"))
                            {
                                spec.Comment = "MS2 deconvoluted using CorrDec from all ion fragmentation data; MetaboLights identifier MTBLS1040";
                            }
                            else
                            {
                                spec.Comment = "MS2 deconvoluted using MS2Dec from all ion fragmentation data; MetaboLights identifier MTBLS1040; " + spec.Comment;
                            }
                        }
                    }
                });
            }
        }

        // Change MsLevel of CorrDec
        private DelegateCommand temporaryMethod2;
        public DelegateCommand TemporaryMethods2 {
            get {
                return temporaryMethod2 ?? new DelegateCommand(x =>
                {
                    if (this.CompoundTable == null) return;
                    foreach (var c in CompoundTable)
                    {
                        foreach (var spec in c.Spectra)
                        {
                            Console.WriteLine(spec.CollisionEnergy);
                            if (spec.CollisionEnergy == 0)
                            {
                                Console.WriteLine("working");
                                spec.MsLevel = "MS1";
                            }
                        }
                    }
                });
            }
        }

        // CorrDec vs MS2Dec similarity in DIA GIAR library
        private DelegateCommand temporaryMethod3;
        public DelegateCommand TemporaryMethods3 {
            get {
                return temporaryMethod3 ?? new DelegateCommand(x =>
                {
                    if (this.CompoundTable == null) return;
                    using (var sw = new System.IO.StreamWriter(System.IO.Path.GetDirectoryName(MsLimaData.DataStorage.FilePath) + "\\ms2sim.tsv", false, Encoding.UTF8))
                    {
                        foreach (var c in CompoundTable)
                        {
                            c.Name = c.Name.Replace(',', '_');
                            if (c.NumSpectra == 3)
                            {
                                sw.WriteLine(c.InChIKey + "\t" + c.Name + "\t" + c.NumSpectra + "\t");
                                continue;
                            }

                            var dic = new Dictionary<float, MassSpectrum>();
                            foreach (var spec in c.Spectra)
                            {
                                if (dic.ContainsKey(spec.CollisionEnergy))
                                {
                                    var s1 = spec;
                                    var s2 = dic[spec.CollisionEnergy];
                                    if (spec.Comment.Contains("CorrDec"))
                                    {
                                        s1 = dic[spec.CollisionEnergy];
                                        s2 = spec;
                                    }

                                    var ms2tol = (float)MsLimaData.Parameter.MS2Tol;
                                    var dotProductFactor = 1.0;
                                    var reverseDotProdFactor = 1.0;
                                    var presensePercentageFactor = 1.0;

                                    var dotScore = (float)MsSimilarityScoring.GetMassSpectraSimilarity(s1, s2, ms2tol) * 100;
                                    var revScore = (float)MsSimilarityScoring.GetReverseSearchSimilarity(s1, s2, ms2tol) * 100;
                                    var matchScore = (float)MsSimilarityScoring.GetPresenceSimilarityBasedOnReference(s1, s2, ms2tol) * 100;
                                    var totalScore = (float)((dotProductFactor * dotScore + reverseDotProdFactor * revScore + presensePercentageFactor * matchScore) / (dotProductFactor + reverseDotProdFactor + presensePercentageFactor));

                                    var revScore2 = (float)MsSimilarityScoring.GetReverseSearchSimilarity(s2, s1, ms2tol) * 100;
                                    var matchScore2 = (float)MsSimilarityScoring.GetPresenceSimilarityBasedOnReference(s2, s1, ms2tol) * 100;
                                    var totalScore2 = (float)((dotProductFactor * dotScore + reverseDotProdFactor * revScore2 + presensePercentageFactor * matchScore2) / (dotProductFactor + reverseDotProdFactor + presensePercentageFactor));

                                    sw.Write(c.InChIKey + "\t" + c.Name + "\t" + c.NumSpectra + "\t");
                                    sw.WriteLine(spec.CollisionEnergy + "\t" + dotScore + "\t" + revScore + "\t" + matchScore + "\t" + totalScore + "\t" + revScore2 + "\t" + matchScore2 + "\t" + totalScore2);
                                }
                                else
                                {
                                    dic.Add(spec.CollisionEnergy, spec);
                                }
                            }

                        }
                    }
                });
            }
        }

        // CorrDecやMS2Dec(DIA; GIAR) vs. DDA (KI)を比較したときの結果をtxt, pngで保存。
        public DelegateCommand TemporaryMethods4 {
            get {
                return new DelegateCommand(x => {
                    if (this.CompoundTable == null) return;
                    using (var sw = new System.IO.StreamWriter(System.IO.Path.GetDirectoryName(MsLimaData.DataStorage.FilePath) + "\\ms2sim.tsv", false, Encoding.UTF8))
                    {
                        foreach (var c in CompoundTable)
                        {
                            c.Name = c.Name.Replace(',', '_');
                            if (c.NumSpectra != 10)
                            {
                                continue;
                            }

                            var dic1 = new Dictionary<float, MassSpectrum>();
                            var dic2 = new Dictionary<float, MassSpectrum>();
                            foreach (var spec in c.Spectra)
                            {
                                if (dic1.ContainsKey(spec.CollisionEnergy))
                                {
                                    if (dic2.ContainsKey(spec.CollisionEnergy))
                                    {
                                        var s1 = spec;
                                        var s2 = dic1[spec.CollisionEnergy];
                                        var s3 = dic2[spec.CollisionEnergy];

                                        var ms2tol = (float)MsLimaData.Parameter.MS2Tol;

                                        var dotScore1 = (float)MsSimilarityScoring.GetMassSpectraSimilarity(s2, s1, ms2tol) * 100;
                                        var dotScore2 = (float)MsSimilarityScoring.GetMassSpectraSimilarity(s3, s1, ms2tol) * 100;

                                        sw.Write(c.InChIKey + "\t" + c.Name + "\t" + c.NumSpectra + "\t");
                                        sw.WriteLine(spec.CollisionEnergy + "\t" + dotScore1 + "\t" + dotScore2);

                                        var s1rel = MassSpectrumUtility.ConvertToRelativeIntensity(s1);
                                        var s2rel = MassSpectrumUtility.ConvertToRelativeIntensity(s2);
                                        var s3rel = MassSpectrumUtility.ConvertToRelativeIntensity(s3);
                                        var f1 = System.IO.Path.GetDirectoryName(MsLimaData.DataStorage.FilePath) + "\\" + c.Id +"_"+ "CorrDec_" + Math.Round(dotScore1, 1) + "_" + spec.CollisionEnergy + "_" + c.Name;
                                        var f2 = System.IO.Path.GetDirectoryName(MsLimaData.DataStorage.FilePath) + "\\" + c.Id +"_"+ "MS2Dec_" + Math.Round(dotScore2, 1) + "_" + spec.CollisionEnergy + "_" + c.Name;
                                        var dv1 = MsHandler.GetMassSpectrumWithRefDrawVisual(s2rel, s1rel);
                                        var dv2 = MsHandler.GetMassSpectrumWithRefDrawVisual(s3rel, s1rel);
                                        dv1.Title.Label = "CorrDec; " + s1.CollisionEnergy + "eV; " + Math.Round((dotScore1), 1) + "%; " + s2.Name;
                                        dv2.Title.Label = "MS2Dec; " + s2.CollisionEnergy + "eV; " + Math.Round((dotScore2), 1) + "%; " + s3.Name;
                                        Exporter.ExportDrawVisual.SaveAsPng(f1 + ".png", dv1, dv1.MinX, dv1.MaxX, dv1.MinY, dv1.MaxY, 350, 300, 300, 300, true);
                                        Exporter.ExportDrawVisual.SaveAsPng(f2 + ".png", dv2, dv2.MinX, dv2.MaxX, dv2.MinY, dv2.MaxY, 350, 300, 300, 300, true);
                                    }
                                    else
                                    {
                                        dic2.Add(spec.CollisionEnergy, spec);
                                    }
                                }
                                else
                                {
                                    dic1.Add(spec.CollisionEnergy, spec);
                                }
                            }

                        }
                    }

                });
            }
        }

        // 2つのMSPファイルを開き、後に開いた方のprecursor mzを、先に開いた方のコメント欄に追記する。
        // なお、その時にCorrDecとコメントに入っている場合は何もしない。
        public DelegateCommand TemporaryMethods5 {
            get {
                return new DelegateCommand(x =>
                {
                    Microsoft.Win32.OpenFileDialog ofd = new Microsoft.Win32.OpenFileDialog
                    {
                        Filter = "MSP file(*.msp)|*.msp| MGF file (*.mgf)|*.mgf| Text file (*.txt)|*.txt| all files(*)|*;",
                        Title = "Import a library file",
                        RestoreDirectory = true,
                        Multiselect = false
                    };

                    if (ofd.ShowDialog() == true)
                    {
                        var ms1 = Reader.ReadMspFile.ReadAsMsSpectra(ofd.FileName);

                        Microsoft.Win32.OpenFileDialog ofd2 = new Microsoft.Win32.OpenFileDialog
                        {
                            Filter = "MSP file(*.msp)|*.msp| MGF file (*.mgf)|*.mgf| Text file (*.txt)|*.txt| all files(*)|*;",
                            Title = "Import a library file",
                            RestoreDirectory = true,
                            Multiselect = false
                        };

                        if (ofd2.ShowDialog() == true)
                        {
                            var ms2 = Reader.ReadMspFile.ReadAsMsSpectra(ofd2.FileName);


                            for (var i = 0; i < ms1.Count; i++)
                            {
                                if (ms1[i].Comment.Contains("CorrDec")) continue;
                                var s = ms2[i].PrecursorMz;
                                ms1[i].Comment = "Experimental precursorMz " + Math.Round(s,5) + "; " + ms1[i].Comment;
                            }

                        }
                        using (var sw = new System.IO.StreamWriter(ofd.FileName + "mod", false, Encoding.UTF8))
                        {
                            Writer.MassSpectrumWriter.WriteMassSpectraAsMsp(sw, ms1);
                        }
                    }
                });
            }
        }

        // CorrDec revision用
        // Tyrosineの低段階希釈サンプルをCorrDecで全通り計算したので、それらを比較するためのメソッド。
        // 対象とするライブラリを変更することも可。2つめに開いたライブラリが対象ライブラリ。
        public DelegateCommand TemporaryMethods6 {
            get {
                return new DelegateCommand(x =>
                {
                    Microsoft.Win32.OpenFileDialog ofd = new Microsoft.Win32.OpenFileDialog
                    {
                        Filter = "MSP file(*.msp)|*.msp| MGF file (*.mgf)|*.mgf| Text file (*.txt)|*.txt| all files(*)|*;",
                        Title = "Import a library file",
                        RestoreDirectory = true,
                        Multiselect = false
                    };

                    if (ofd.ShowDialog() == true)
                    {
                        var ms1 = Reader.ReadMspFile.ReadAsMsSpectra(ofd.FileName);

                        Microsoft.Win32.OpenFileDialog ofd2 = new Microsoft.Win32.OpenFileDialog
                        {
                            Filter = "MSP file(*.msp)|*.msp| MGF file (*.mgf)|*.mgf| Text file (*.txt)|*.txt| all files(*)|*;",
                            Title = "Import a library file",
                            RestoreDirectory = true,
                            Multiselect = false
                        };

                        if (ofd2.ShowDialog() == true)
                        {
                            using (var sw = new System.IO.StreamWriter(System.IO.Path.GetDirectoryName(MsLimaData.DataStorage.FilePath) + "\\ms2similarity_tyrosine_serires.tsv", false, Encoding.UTF8))
                            {
                                var resMatrix = new double[100,100];
                                // ms2には1つだけmass spectrum入れておくこと。
                                var ms2 = Reader.ReadMspFile.ReadAsMsSpectra(ofd2.FileName);
                                var s2rel = MassSpectrumUtility.ConvertToRelativeIntensity(ms2[0]);
                                var s2relv2 = new MassSpectrum() { Spectrum = new List<AnnotatedPeak>() };
                                foreach (var spec in s2rel.Spectrum)
                                {
                                    if (spec.Intensity < 1) continue;
                                    s2relv2.Spectrum.Add(spec);
                                }

                                for (var i = 0; i < ms1.Count; i++)
                                {
                                    var start = 0;
                                    var end = 0;
                                    foreach (var meta in ms1[i].OtherMetaData)
                                    {
                                        Console.WriteLine(meta);
                                        if (meta.Contains("Start: "))
                                        {
                                            var tmps = meta.Split(':')[1].Trim();
                                            start = int.Parse(tmps);
                                            Console.WriteLine(start);
                                        }
                                        else if (meta.Contains("End: "))
                                        {
                                            var tmps = meta.Split(':')[1].Trim();
                                            end = int.Parse(tmps);
                                        }
                                    }

                                    if (ms1[i].Spectrum.Count == 0)
                                    {
                                        resMatrix[start, end] = 0.0;
                                        continue;
                                    }

                                    var s1rel = MassSpectrumUtility.ConvertToRelativeIntensity(ms1[i]);
                                    var s1relv2 = new MassSpectrum() { Spectrum = new List<AnnotatedPeak>() };
                                    foreach (var spec in s1rel.Spectrum)
                                    {
                                        if (spec.Intensity < 1) continue;
                                        s1relv2.Spectrum.Add(spec);
                                    }

                                    s1relv2.Name = s1rel.Name;
                                    resMatrix[start,end] = MsSimilarityScoring.GetMassSpectraSimilarity(s1relv2, s2relv2, 0.01f) * 100;

                                }
                                // header
                                sw.Write("i/j" + "\t");
                                for(var i = 0; i < 100; i++)
                                {
                                    sw.Write(i + "\t");
                                }
                                sw.WriteLine("");

                                for(var i = 0; i < 100; i++)
                                {
                                    // rownames
                                    sw.Write(i + "\t");
                                    for(var j = 0; j < 100; j++)
                                    {
                                        sw.Write(resMatrix[i, j] + "\t");
                                    }
                                    sw.WriteLine("");
                                }
                            }
                        }
                    }
                });
            }
        }

        public DelegateCommand TemporaryMethods7 {
            get {
                return new DelegateCommand(x =>
                {
                    var filePath = MsLimaData.DataStorage.FilePath + ".removeLowAccuracyMS.msp";
                    using (var sw = new System.IO.StreamWriter(filePath, false, Encoding.UTF8))
                    {
                        var result = new List<List<AnnotatedPeak>>();
                        foreach (var i in MsLimaData.DataStorage.CompoundList)
                        {
                            var spectra = new List<MassSpectrum>();
                            foreach (var c in i.Spectra)
                            {
                                if (IsHighAccuracyMS(c.Spectrum))
                                {
                                    spectra.Add(c);
                                }
                            }
                            Writer.MassSpectrumWriter.WriteMassSpectraAsMsp(sw, spectra);
                        }
                    }
                });
            }
        }

        private bool IsHighAccuracyMS(List<AnnotatedPeak> msp)
        {
            int counter = 0;
            if (msp.Count < 2) return false;
            foreach (var s in msp)
            {
                var precision = GetPrecision(s.Mz);
                if (precision < 2)
                {
                    counter += 1;
                }
                if(counter == 2)
                {
                    return false;
                }
            }
            return true;
        }

        private int GetPrecision(double val)
        {
            string priceString = val.ToString().TrimEnd('0');

            int index = priceString.IndexOf('.');
            if (index == -1)
                return 0;

            return priceString.Substring(index + 1).Length;
        }


        private DelegateCommand saveChart;

        public DelegateCommand SaveChart{
            get {
                return saveChart ?? new DelegateCommand(x => {
                    var w = new SaveChartDrawing(((DrawVisualMassSpectrum)x));
                    w.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen;
                    w.Show();
                });
            }
       }

        #endregion
        #endregion

        public MainWindowVM()
        {
            //Task.Run(() => SmilesUtility.TryClassLoad());
            MsLimaData = new MsLimaData();
            MsHandler = new MassSpectrumViewHandler(MsLimaData.Parameter);
            AutoExporter = new AutoRepeater(MsLimaData.Parameter.WinParam.AutoExportIntervalMillisecond);
            AutoExporter.OnTimeEventHandler += (o, e) => { AutoExportFunction(); };
            SingleMassSpectrumUI = new MassSpectrumUI(SingleMassSpectrumVM);
            ConsensusSpectrumUI = new MassSpectrumUI(ConsensusSpectrumVM);

            ControlRefresh = new ControlRefresh(this);
            SetCommands();
            var i = 120.123f;
            Console.WriteLine(i);
            Console.WriteLine((double)i);
            var i2 =(double)i * 2;
            Console.WriteLine(i + " * 2 = " + i2);
            var i3 = (float)i2 / 2.0f;
            Console.WriteLine("i = " + i3);

        }

        private void MainWindowLoad()
        {
            //WindowUtility.StartUpInitializingWindow();
        }

        private void CheckRtDifferences()
        {
            if (CompoundTable == null) return;
            var rtString = "";
            var formulaString = "";
            var InChIKeyString = "";
            CompoundGroupUtility.CheckCompoundList(CompoundTable, MsLimaData.Parameter.RtTol, ref rtString, ref formulaString, ref InChIKeyString);
            WindowUtility.StartUpErrorMessageWindow("RT difference", rtString);
        }

        private void CheckInChIKeyDifferences()
        {
            if (CompoundTable == null) return;
            var rtString = "";
            var formulaString = "";
            var InChIKeyString = "";
            CompoundGroupUtility.CheckCompoundList(CompoundTable, MsLimaData.Parameter.RtTol, ref rtString, ref formulaString, ref InChIKeyString);
            WindowUtility.StartUpErrorMessageWindow("InChIKey difference", InChIKeyString);
        }

        private void CheckFormulaDifferences()
        {
            if (CompoundTable == null) return;
            var rtString = "";
            var formulaString = "";
            var InChIKeyString = "";
            CompoundGroupUtility.CheckCompoundList(CompoundTable, MsLimaData.Parameter.RtTol, ref rtString, ref formulaString, ref InChIKeyString);
            WindowUtility.StartUpErrorMessageWindow("Formula difference", formulaString);
        }

        private void SetCommands()
        {
            Debug.WriteLine("SetCommands: Initializing all commands...");
            WindowLoaded = new DelegateCommand(x => MainWindowLoad());

            #region MenuItems
            ImportFileCommand = new DelegateCommand(
                x =>
                {
                    // Handle UI interactions on main thread, then process file async
                    ImportFileWithProperThreading();
                }
            );

            ImportMassBankFileCommand = new DelegateCommand(
                x =>
                {
                    ImportUtility.ImportMassBankFile(MsLimaData, AutoExporter);
                    ImportFile();
                }
            );

            SaveAsMspCommand = new DelegateCommand(
                x => ExportUtility.SaveAsMsp(CompoundTable),
                x => !IsDataLoaded());

            SaveAsMspWithoutRTCommand = new DelegateCommand(
                x => ExportUtility.SaveAsMspWithoutRT(CompoundTable),
                x => !IsDataLoaded());
            SaveAsMgfCommand = new DelegateCommand(
                x => ExportUtility.SaveAsMgf(CompoundTable),
                x => !IsDataLoaded());
            SaveAsMzMineCommand = new DelegateCommand(
                x => ExportUtility.SaveCompoundTableAsMzMineFormat(CompoundTable),
                x => !IsDataLoaded());

            ConvertAccurateMassToTheoreticalMass = new DelegateCommand(
                x => CompoundGroupUtility.ConvertActualMassToTheoreticalMass(CompoundTable),
                x => !IsDataLoaded());

            DropRetentionTime = new DelegateCommand(
                x => CompoundGroupUtility.DropRetentionTime(CompoundTable),
                x => !IsDataLoaded());

            RemoveUnannotatedCommand = new DelegateCommand(
                x => CompoundGroupUtility.RemoveUnannotatedPeaks(CompoundTable),
                x => !IsDataLoaded());

            SaveCommonProductIonCommand = new DelegateCommand(
                x => ExportUtility.SaveCommonProductIonTable(CompoundTable),
                x => !IsDataLoaded());

            UpdateSmilesAndInChiBasedOnInChIKeyCommand = new DelegateCommand(
                x => WindowUtility.UpdateMetaData(CompoundTable),
                x => !IsDataLoaded());

            UpdateCommonMetaDataCommand = new DelegateCommand(
              x => WindowUtility.UpdateCommonMetaData(CompoundTable),
              x => !IsDataLoaded());

            CheckRtDifferencesCommand = new DelegateCommand(
                x => CheckRtDifferences(),
                x => !IsDataLoaded());

            CheckInChIKeyDifferencesCommand = new DelegateCommand(
                x => CheckInChIKeyDifferences(),
                x => !IsDataLoaded());

            CheckFormulaDifferencesCommand = new DelegateCommand(
                x => CheckFormulaDifferences(),
                x => !IsDataLoaded());

            CheckAllValidationCommand = new DelegateCommand(
                x => WindowUtility.CheckCompoundGroup(CompoundTable, MsLimaData.Parameter.RtTol),
                x => !IsDataLoaded());

            // Filter Commands - ULTRA-ROBUST: Always enabled with comprehensive error handling
            Debug.WriteLine("SetCommands: Creating ULTRA-ROBUST ClearAllFiltersCommand...");
            ClearAllFiltersCommand = new DelegateCommand(
                x => {
                    try
                    {
                        Debug.WriteLine("ULTRA-ROBUST Clear command executed!");
                        ClearAllFilters();
                        Debug.WriteLine("ULTRA-ROBUST Clear command completed successfully!");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"CRITICAL ERROR in Clear command: {ex.Message}");
                        Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                        // Show error but don't crash the application
                        try
                        {
                            MessageBox.Show($"Error clearing filters: {ex.Message}\n\nThe application will continue to work normally.",
                                          "Clear Filters Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                        catch
                        {
                            Debug.WriteLine("CRITICAL: Even error message display failed in Clear command");
                        }
                    }
                },
                x => {
                    try
                    {
                        Debug.WriteLine("Clear command CanExecute checked - returning true");
                        return true; // Always enabled
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error in Clear command CanExecute: {ex.Message}");
                        return true; // Always return true even if there's an error
                    }
                });
            Debug.WriteLine("SetCommands: ULTRA-ROBUST ClearAllFiltersCommand created successfully");

            #endregion


            #region SelectionChanged
            SelectionChangedSingleSpectrumTableCommand = new DelegateCommand(
                x => SingleSpectrumTableSelectionChanged()
                );

            SelectionChangedConsensusTableCommand = new DelegateCommand(
                x => ConsensusTableSelectionChanged()
                );
            SelectionChangedTabControlMsTableCommand = new DelegateCommand(
                x => SelectionChangedTabConrtrolMsView()
                );
            #endregion


            #region launch window
            StartUpSettingWindow = new DelegateCommand(
                x => WindowUtility.StartUpParameterSettingWindow(MsLimaData));

            StartUpWindowAllSpectra = new DelegateCommand(
                x => WindowUtility.StartUpAllSpectraTableWindow(MsLimaData),
                x => !IsDataLoaded());
            StartUpWindowComparativeViewer = new DelegateCommand(
                x => WindowUtility.StartUpComparativeSpectraViewer(MsLimaData),
                x => !IsDataLoaded());
            #endregion
        }


        #region Methods for SelectionChanged Command

        public void SingleSpectrumTableSelectionChanged()
        {
            ControlRefresh.SelectedPeakChanged(TabMassSpectraView);
        }

        public void ConsensusTableSelectionChanged()
        {
            ControlRefresh.SelectedConsensusPeakChanged(TabMassSpectraView);
        }

        public void SelectionChangedTabConrtrolMsView()
        {
            SingleMassSpectrumRefresh();
            MsSpectraViewRefresh();
        }

        public void SelectedCompoundChanged()
        {
            OnPropertyChanged(nameof(LabelSelectedCompound));
            OnPropertyChanged(nameof(ConsensusSpectraTable));
            ShouldUpdateMultipleSpectrumVM = true;
            ShouldUpdateConsensusSpectrumVM = true;
            SelectedSpectrum = selectedCompoundBean.Spectra[0];

            // OPTIMIZATION: Always update structure image when compound changes
            // This will also handle any deferred theme-related structure image updates
            UpdateStructureImage();

            MsSpectraViewRefresh();
        }

        /// <summary>
        /// Updates the structure image for the currently selected compound
        /// This method is called when the compound changes or when the theme changes
        /// </summary>
        public void UpdateStructureImage()
        {
            try
            {
                if (SelectedCompoundBean != null && !string.IsNullOrEmpty(SelectedCompoundBean.Smiles))
                {
                    // Optimized image size for better visibility and centering - reduced from 800x800 for better scaling
                    StructureImage = SmilesUtility.SmilesToMediaImageSource(SelectedCompoundBean.Smiles, 400, 400);

                    // Notify that the structure image has changed
                    OnPropertyChanged(nameof(StructureImage));
                }
                else
                {
                    // Clear the structure image if no compound or SMILES data
                    StructureImage = null;
                    OnPropertyChanged(nameof(StructureImage));
                }
            }
            catch (System.IO.FileNotFoundException ex) when (ex.Message.Contains("System.Drawing.Common"))
            {
                // Handle missing System.Drawing.Common dependency gracefully
                System.Diagnostics.Debug.WriteLine($"System.Drawing.Common dependency missing: {ex.Message}");
                StructureImage = null;
                OnPropertyChanged(nameof(StructureImage));

                // Show user-friendly message only once per session
                if (!_structureErrorShown)
                {
                    _structureErrorShown = true;
                    MessageBox.Show("Chemical structure visualization is temporarily unavailable due to a missing dependency. " +
                                  "The application will continue to work normally for all other features.",
                                  "Structure Display Notice", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                // Handle any other errors in structure image generation
                System.Diagnostics.Debug.WriteLine($"Error generating structure image: {ex.Message}");
                StructureImage = null;
                OnPropertyChanged(nameof(StructureImage));

                // Show user-friendly message only once per session
                if (!_structureErrorShown)
                {
                    _structureErrorShown = true;
                    MessageBox.Show($"Error generating chemical structure image: {ex.Message}\n\n" +
                                  "The application will continue to work normally for all other features.",
                                  "Structure Display Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
        }

        public void SelectedSpectrumChanged()
        {
            OnPropertyChanged(nameof(LabelSelectedSpectra));
            ShouldUpdateSingleMassSpectrumVM = true;
            SingleMassSpectrumRefresh();
        }

        public void SingleMassSpectrumRefresh()
        {
            if (TabMassSpectraView == TabMassSpectraView.SingleMS && ShouldUpdateSingleMassSpectrumVM)
            {
                SingleMassSpectrumVM = MsHandler.GetMassSpectrumDrawVisual(SelectedSpectrum);
                ShouldUpdateSingleMassSpectrumVM = false;
            }
        }

        public void MsSpectraViewRefresh()
        {
            if (TabMassSpectraView == TabMassSpectraView.MultipleMS && ShouldUpdateMultipleSpectrumVM)
            {
                MultipleSpectra = ControlRefresh.MultipleSpectraRefresh();
                ShouldUpdateMultipleSpectrumVM = false;
            }
            else if (TabMassSpectraView == TabMassSpectraView.ConsensusMS && ShouldUpdateConsensusSpectrumVM)
            {
                ConsensusSpectrumVM = MsHandler.GetMassSpectrumDrawVisualFromConsensus(ConsensusSpectraTable);
                ShouldUpdateConsensusSpectrumVM = false;
            }
        }
        #endregion


        #region Methods for MenuItem Commands

        /// <summary>
        /// PROFESSIONAL MSP IMPORT with enhanced progress reporting and user experience
        /// </summary>
        private async void ImportFileWithProperThreading()
        {
            var importStopwatch = Stopwatch.StartNew();

            try
            {
                // Step 1: Handle UI interactions on main thread (STA)
                var res = MessageBox.Show("Do you want to open with auto saving?", "Ask", MessageBoxButton.YesNo);

                OpenFileDialog ofd = new OpenFileDialog
                {
                    Filter = "MSP file(*.msp)|*.msp| MGF file (*.mgf)|*.mgf| Text file (*.txt)|*.txt| all files(*)|*;",
                    Title = "Import a library file",
                    RestoreDirectory = true,
                    Multiselect = false
                };

                if (ofd.ShowDialog() == true)
                {
                    // Remove total time display as requested - no longer showing elapsed time
                    if (AutoExporter.ExportTimer.Enabled) AutoExporter.Stop();

                    // Get file information for enhanced progress reporting
                    var fileInfo = new FileInfo(ofd.FileName);
                    var fileSizeMB = fileInfo.Length / (1024.0 * 1024.0);
                    var estimatedSpectra = (int)(fileInfo.Length / 1500); // Estimate spectra count

                    Debug.WriteLine($"Starting professional import of {fileSizeMB:F2}MB file: {ofd.FileName}");
                    Debug.WriteLine($"Estimated {estimatedSpectra:N0} spectra for processing");

                    // Initialize progress bar
                    IsImportInProgress = true;
                    ImportProgress = 0;
                    IsImportIndeterminate = false;

                    // Show cursor to indicate processing
                    Mouse.OverrideCursor = Cursors.Wait;

                    try
                    {
                        // Step 3: Create enhanced progress reporter with smooth updates
                        var progress = new Progress<int>(percentage =>
                        {
                            // Enhanced progress reporting with UI progress bar updates
                            Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                            {
                                ImportProgress = percentage;
                                Debug.WriteLine($"Import progress: {percentage}% ({estimatedSpectra * percentage / 100:F0} spectra processed)");
                            }), System.Windows.Threading.DispatcherPriority.Background);
                        });

                        // Step 4: Process file on background thread with enhanced progress reporting
                        await Task.Run(async () =>
                        {
                            try
                            {
                                Debug.WriteLine("Starting import operation...");

                                if (res == MessageBoxResult.Yes)
                                {
                                    Debug.WriteLine("Creating backup copy...");

                                    var dt = DateTime.Now;
                                    var newFilePath = System.IO.Path.GetDirectoryName(ofd.FileName) + "\\" +
                                                    System.IO.Path.GetFileNameWithoutExtension(ofd.FileName) + "_StartMod_" +
                                                    dt.ToString("yy_MM_dd_HH_mm_ss") + System.IO.Path.GetExtension(ofd.FileName);
                                    System.IO.File.Copy(ofd.FileName, newFilePath, true);

                                    Debug.WriteLine("Reading MSP file with progress reporting...");
                                    await MsLimaData.DataStorage.SetLibraryAsync(newFilePath, MsLimaData.Parameter.CompoundGroupingKey, progress);
                                    MsLimaData.DataStorage.OriginalFilePath = ofd.FileName;

                                    // Start auto exporter on main thread
                                    Application.Current.Dispatcher.Invoke(() => AutoExporter.Start());
                                }
                                else
                                {
                                    Debug.WriteLine("Reading MSP file with progress reporting...");
                                    await MsLimaData.DataStorage.SetLibraryAsync(ofd.FileName, MsLimaData.Parameter.CompoundGroupingKey, progress);
                                }

                                Debug.WriteLine("Finalizing import...");
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"Error during background import: {ex.Message}");
                                throw;
                            }
                        });

                        // Step 5: Update UI on main thread after import completes
                        Debug.WriteLine("Setting up user interface...");
                        ImportFile();

                        // Step 6: Complete the import successfully
                        importStopwatch.Stop();
                        var totalSpectra = MsLimaData.DataStorage.RawLibraryFile?.Count ?? 0;
                        var totalCompounds = MsLimaData.DataStorage.CompoundList?.Count ?? 0;

                        Debug.WriteLine($"PROFESSIONAL IMPORT COMPLETED:");
                        Debug.WriteLine($"  File size: {fileSizeMB:F2}MB");
                        Debug.WriteLine($"  Total import time: {importStopwatch.Elapsed:mm\\:ss}");
                        Debug.WriteLine($"  Compounds loaded: {totalCompounds}");
                        Debug.WriteLine($"  Spectra loaded: {totalSpectra}");

                        // Calculate performance metrics
                        var spectraPerSecond = totalSpectra / Math.Max(importStopwatch.Elapsed.TotalSeconds, 0.001);
                        var mbPerSecond = fileSizeMB / Math.Max(importStopwatch.Elapsed.TotalSeconds, 0.001);

                        Debug.WriteLine($"  Performance: {spectraPerSecond:F0} spectra/sec, {mbPerSecond:F2} MB/sec");

                        // Show success message for large files (without total time as requested)
                        if (fileSizeMB > 5.0)
                        {
                            MessageBox.Show($"Successfully imported {fileSizeMB:F2}MB file with {totalCompounds:N0} compounds and {totalSpectra:N0} spectra.\n\nPerformance: {spectraPerSecond:F0} spectra/sec",
                                          "Import Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Import error: {ex.Message}");

                        MessageBox.Show($"Error importing file: {ex.Message}\n\nFile: {ofd.FileName}\nSize: {fileSizeMB:F2}MB\n\nStack trace:\n{ex.StackTrace}",
                                      "Import Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    finally
                    {
                        // Hide progress bar and reset cursor
                        IsImportInProgress = false;
                        ImportProgress = 0;
                        Mouse.OverrideCursor = null;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Import process error: {ex.Message}");

                MessageBox.Show($"Critical error during import process: {ex.Message}\n\nStack trace:\n{ex.StackTrace}",
                              "Import Process Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// OPTIMIZED UI SETUP after import - based on original fast implementation
        /// </summary>
        public void ImportFile()
        {
            if (MsLimaData.DataStorage.CompoundList == null || MsLimaData.DataStorage.CompoundList.Count == 0) return;
            FilteredCompoundTable = new FilteredTable(this.CompoundTable);
            FilteredTableSetting = new FilterSettingsForLibrary(this.FilteredCompoundTable.View);
            FilteredCompoundTable.View.Filter = this.FilteredTableSetting.CompoundFilter;
            MainWindowTitle = Properties.Resources.Version + " File: " + MsLimaData.DataStorage.FilePath;
            SelectedCompoundBean = CompoundTable[0];
            OnPropertyChangedAfterFileImported();
        }

        #endregion

        #region Utilities
        private void OnPropertyChangedAfterFileImported()
        {
            StartUpWindowComparativeViewer.RaiseCanExecuteChanged();
            StartUpWindowAllSpectra.RaiseCanExecuteChanged();
            SaveAsMspCommand.RaiseCanExecuteChanged();
            SaveAsMspWithoutRTCommand.RaiseCanExecuteChanged();
            SaveAsMzMineCommand.RaiseCanExecuteChanged();
            UpdateSmilesAndInChiBasedOnInChIKeyCommand.RaiseCanExecuteChanged();
            ConvertAccurateMassToTheoreticalMass.RaiseCanExecuteChanged();
            DropRetentionTime.RaiseCanExecuteChanged();
            RemoveUnannotatedCommand.RaiseCanExecuteChanged();
            SaveCommonProductIonCommand.RaiseCanExecuteChanged();
            UpdateCommonMetaDataCommand.RaiseCanExecuteChanged();
            CheckRtDifferencesCommand.RaiseCanExecuteChanged();
            CheckInChIKeyDifferencesCommand.RaiseCanExecuteChanged();
            CheckFormulaDifferencesCommand.RaiseCanExecuteChanged();
            CheckAllValidationCommand.RaiseCanExecuteChanged();
            // FIXED: Include Clear command in CanExecute refresh
            ClearAllFiltersCommand.RaiseCanExecuteChanged();
            OnPropertyChanged(nameof(FilteredCompoundTableView));
            OnPropertyChanged(nameof(LabelNumCompounds));
            OnPropertyChanged(nameof(LabelNumSpectra));
        }

        private bool IsDataLoaded()
        {
            return (CompoundTable == null || CompoundTable.Count == 0);
        }

        public void AutoExportFunction()
        {
            Task.Run(() => ExportCompoundTable.ExportCompoundTableAsMsp(MsLimaData.DataStorage.FilePath, MsLimaData.DataStorage.CompoundList));
        }


        #endregion
    }
}
