﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Diagnostics;
using Metabolomics.MsLima.Bean;
using Metabolomics.Core.Utility;
using Metabolomics.MsLima;
using System.IO;

namespace Metabolomics.MsLima.Model
{
    public static class CompoundGroupUtility
    {
        /// <summary>
        /// High-performance asynchronous compound list creation with progress reporting
        /// </summary>
        public static async Task<List<CompoundBean>> CreateCompoundListAsync(List<MassSpectrum> rawLibraryFile, CompoundGroupingKey key, IProgress<int> progress = null)
        {
            return await Task.Run(() =>
            {
                var stopwatch = Stopwatch.StartNew();
                List<CompoundBean> result;

                if (key == CompoundGroupingKey.ShortInChIKey)
                {
                    result = CreateCompoundListByShortInChIKey(rawLibraryFile, progress);
                }
                else if (key == CompoundGroupingKey.InChIKey)
                {
                    result = CreateCompoundListByInChIKey(rawLibraryFile, progress);
                }
                else if (key == CompoundGroupingKey.InChI)
                {
                    result = CreateCompoundListByInChI(rawLibraryFile, progress);
                }
                else if (key == CompoundGroupingKey.None)
                {
                    result = CreateCompoundListByNone(rawLibraryFile, progress);
                }
                else
                {
                    result = null;
                }

                stopwatch.Stop();
                Debug.WriteLine($"CreateCompoundListAsync completed in {stopwatch.ElapsedMilliseconds}ms for {rawLibraryFile?.Count ?? 0} spectra");
                return result;
            });
        }

        public static List<CompoundBean> CreateCompoundList(List<MassSpectrum> rawLibraryFile, CompoundGroupingKey key)
        {
            if(key == CompoundGroupingKey.ShortInChIKey)
            {
                return CreateCompoundListByShortInChIKey(rawLibraryFile);
            }
            else if(key == CompoundGroupingKey.InChIKey)
            {
                return CreateCompoundListByInChIKey(rawLibraryFile);
            }
            else if (key == CompoundGroupingKey.InChI)
            {
                return CreateCompoundListByInChI(rawLibraryFile);
            }
            else if(key == CompoundGroupingKey.None)
            {
                return CreateCompoundListByNone(rawLibraryFile);

            }
            else
            {
                return null;
            }

        }

        public static List<CompoundBean> CreateCompoundListByNone(List<MassSpectrum> rawLibraryFile, IProgress<int> progress = null)
        {
            if (rawLibraryFile == null || rawLibraryFile.Count == 0)
                return new List<CompoundBean>();

            var counter = 0;
            var totalCount = rawLibraryFile.Count;
            // Pre-allocate list with known capacity for better performance
            var comps = new List<CompoundBean>(totalCount);

            foreach (var spectrum in rawLibraryFile)
            {
                var comp = CreateCompoundFromSpectrum(spectrum, counter);
                comp.NumSpectra++;
                comp.Spectra.Add(spectrum);
                comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                comp.Name = spectrum.Name;
                comp.Formula = spectrum.Formula;
                comp.Smiles = spectrum.Smiles;
                counter++;
                comps.Add(comp);

                // Report progress every 100 compounds to avoid excessive updates
                if (progress != null && counter % 100 == 0)
                {
                    progress.Report((int)((double)counter / totalCount * 100));
                }
            }

            // Report final progress
            progress?.Report(100);
            return comps;
        }

        public static List<CompoundBean> CreateCompoundListByNone(List<MassSpectrum> rawLibraryFile)
        {
            return CreateCompoundListByNone(rawLibraryFile, null);
        }

        /// <summary>
        /// High-performance async version of CreateCompoundListByShortInChIKey with progress reporting
        /// </summary>
        public static List<CompoundBean> CreateCompoundListByShortInChIKey(List<MassSpectrum> rawLibraryFile, IProgress<int> progress = null)
        {
            if (rawLibraryFile == null || rawLibraryFile.Count == 0)
                return new List<CompoundBean>();

            CompoundBean comp;
            var dic = new Dictionary<string, CompoundBean>();
            var InchiKeys = new List<string>();
            var totalCount = rawLibraryFile.Count;
            var processedCount = 0;

            var counter = 1;
            foreach (var spectrum in rawLibraryFile)
            {
                if (string.IsNullOrEmpty(spectrum.InChIKey))
                {
                    comp = CreateCompoundFromSpectrum(spectrum, counter);
                    comp.NumSpectra++;
                    comp.Spectra.Add(spectrum);
                    comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                    comp.Name = spectrum.Name;
                    comp.Formula = spectrum.Formula;
                    comp.Smiles = spectrum.Smiles;
                    dic.Add(counter.ToString(), comp);
                    counter++;
                }
                else
                {
                    var Inchikey = spectrum.InChIKey.Split('-')[0];
                    if (InchiKeys.Contains(Inchikey))
                    {
                        dic[Inchikey].Spectra.Add(spectrum);
                        dic[Inchikey].NumSpectra++;
                    }
                    else
                    {
                        comp = CreateCompoundFromSpectrum(spectrum, counter);
                        comp.NumSpectra++;
                        comp.Spectra.Add(spectrum);
                        comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                        comp.Name = spectrum.Name;
                        comp.Formula = spectrum.Formula;
                        comp.Smiles = spectrum.Smiles;
                        dic.Add(Inchikey, comp);
                        InchiKeys.Add(Inchikey);
                        counter++;
                    }
                }

                processedCount++;
                // Report progress every 100 compounds
                if (progress != null && processedCount % 100 == 0)
                {
                    progress.Report((int)((double)processedCount / totalCount * 100));
                }
            }

            progress?.Report(100);
            return new List<CompoundBean>(dic.Values);
        }

        /// <summary>
        /// High-performance async version of CreateCompoundListByInChIKey with progress reporting
        /// </summary>
        public static List<CompoundBean> CreateCompoundListByInChIKey(List<MassSpectrum> rawLibraryFile, IProgress<int> progress = null)
        {
            if (rawLibraryFile == null || rawLibraryFile.Count == 0)
                return new List<CompoundBean>();

            CompoundBean comp;
            var dic = new Dictionary<string, CompoundBean>();
            var InchiKeys = new List<string>();
            var totalCount = rawLibraryFile.Count;
            var processedCount = 0;

            var counter = 1;
            foreach (var spectrum in rawLibraryFile)
            {
                if (string.IsNullOrEmpty(spectrum.InChIKey))
                {
                    comp = CreateCompoundFromSpectrum(spectrum, counter);
                    comp.NumSpectra++;
                    comp.Spectra.Add(spectrum);
                    comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                    comp.Name = spectrum.Name;
                    comp.Formula = spectrum.Formula;
                    comp.Smiles = spectrum.Smiles;
                    dic.Add(counter.ToString(), comp);
                    counter++;
                }
                else
                {
                    var InChIKey = spectrum.InChIKey;
                    if (InchiKeys.Contains(InChIKey))
                    {
                        dic[InChIKey].Spectra.Add(spectrum);
                        dic[InChIKey].NumSpectra++;
                    }
                    else
                    {
                        comp = CreateCompoundFromSpectrum(spectrum, counter);
                        comp.NumSpectra++;
                        comp.Spectra.Add(spectrum);
                        comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                        comp.Name = spectrum.Name;
                        comp.Formula = spectrum.Formula;
                        comp.Smiles = spectrum.Smiles;
                        dic.Add(InChIKey, comp);
                        InchiKeys.Add(InChIKey);
                        counter++;
                    }
                }

                processedCount++;
                // Report progress every 100 compounds
                if (progress != null && processedCount % 100 == 0)
                {
                    progress.Report((int)((double)processedCount / totalCount * 100));
                }
            }

            progress?.Report(100);
            return new List<CompoundBean>(dic.Values);
        }

        /// <summary>
        /// High-performance async version of CreateCompoundListByInChI with progress reporting
        /// </summary>
        public static List<CompoundBean> CreateCompoundListByInChI(List<MassSpectrum> rawLibraryFile, IProgress<int> progress = null)
        {
            if (rawLibraryFile == null || rawLibraryFile.Count == 0)
                return new List<CompoundBean>();

            CompoundBean comp;
            var dic = new Dictionary<string, CompoundBean>();
            var InChIs = new List<string>();
            var totalCount = rawLibraryFile.Count;
            var processedCount = 0;

            var counter = 1;
            foreach (var spectrum in rawLibraryFile)
            {
                if (string.IsNullOrEmpty(spectrum.InChI))
                {
                    comp = CreateCompoundFromSpectrum(spectrum, counter);
                    comp.NumSpectra++;
                    comp.Spectra.Add(spectrum);
                    comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                    comp.Name = spectrum.Name;
                    comp.Formula = spectrum.Formula;
                    comp.Smiles = spectrum.Smiles;
                    dic.Add(counter.ToString(), comp);
                    counter++;
                }
                else
                {
                    var InChI = spectrum.InChI;
                    if (InChIs.Contains(InChI))
                    {
                        dic[InChI].Spectra.Add(spectrum);
                        dic[InChI].NumSpectra++;
                    }
                    else
                    {
                        comp = CreateCompoundFromSpectrum(spectrum, counter);
                        comp.NumSpectra++;
                        comp.Spectra.Add(spectrum);
                        comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                        comp.Name = spectrum.Name;
                        comp.Formula = spectrum.Formula;
                        comp.Smiles = spectrum.Smiles;
                        dic.Add(InChI, comp);
                        InChIs.Add(InChI);
                        counter++;
                    }
                }

                processedCount++;
                // Report progress every 100 compounds
                if (progress != null && processedCount % 100 == 0)
                {
                    progress.Report((int)((double)processedCount / totalCount * 100));
                }
            }

            progress?.Report(100);
            return new List<CompoundBean>(dic.Values);
        }

        // Helper method to reduce code duplication and improve performance
        private static CompoundBean CreateCompoundFromSpectrum(MassSpectrum spectrum, int id)
        {
            return new CompoundBean
            {
                Id = id,
                InChIKey = spectrum.InChIKey,
                InChI = spectrum.InChI,
                Ontology = spectrum.Ontology,
                IUPACName = spectrum.IUPACname,
                CID = spectrum.CID,
                CAS = spectrum.CAS,
                ExactMass = spectrum.Exactmass,
                MW = spectrum.MW,
                XLogP = spectrum.XLogP,
                MLogP = spectrum.MLogP,
                ALogP = spectrum.ALogP,
                NumBond = spectrum.NumBond,
                NumAtom = spectrum.NumAtom,
                Superclass = spectrum.Superclass,
                Class = spectrum.Class,
                    Subclass = spectrum.Subclass,
                    Cramerrules = spectrum.Cramerrules,
                    SVHC = spectrum.SVHC,
                    CMR = spectrum.CMR,
                    CMRSuspect = spectrum.CMRsuspect,
                    EDC = spectrum.EDC,
                    IARC = spectrum.IARC,
                    Eusml = spectrum.Eusml,
                    ChinaSml = spectrum.ChinaSml,
                    // Critical MS data properties (MISSING ASSIGNMENTS FIXED)
                    PrecursorMz = spectrum.PrecursorMz,
                    IonMode = spectrum.IonMode.ToString(),
                    PrecursorType = spectrum.AdductIon?.AdductIonName ?? "",
                    // Additional metadata
                    Authors = spectrum.Authors,
                    Instrument = spectrum.Instrument,
                    InstrumentType = spectrum.InstrumentType,
                    CollisionEnergy = spectrum.CollisionEnergy,
                    Date = spectrum.Date ?? "",
                    Num_peaks = spectrum.PeakNumber,
                    TopoPSA = spectrum.TopoPSA,
                    // Additional toxicological properties
                    Carcinogenicity_ISS = spectrum.Carcinogenicity_ISS,
                    DNA_alerts_OASIS = spectrum.DNA_alerts_OASIS,
                    DNA_binding_OASIS = spectrum.DNA_binding_OASIS,
                    DNA_binding_OECD = spectrum.DNA_binding_OECD,
                    Protein_binding_alerts_OASIS = spectrum.Protein_binding_alerts_OASIS,
                Vitro_mutagenicity_alerts_ISS = spectrum.Vitro_mutagenicity_alerts_ISS,
                Vivo_mutagenicity_alerts_ISS = spectrum.Vivo_mutagenicity_alerts_ISS
            };
        }
        public static List<CompoundBean> CreateCompoundListByShortInChIKey(List<MassSpectrum> rawLibraryFile)
        {
            if (rawLibraryFile == null || rawLibraryFile.Count == 0)
                return new List<CompoundBean>();

            CompoundBean comp;
            var dic = new Dictionary<string, CompoundBean>();
            var InchiKeys = new List<string>();

            var counter = 1;
            foreach (var spectrum in rawLibraryFile)
            {
                if (string.IsNullOrEmpty(spectrum.InChIKey))
                {
                    comp = new CompoundBean
                    {
                        Id = counter,
                        InChIKey = "",
                        InChI = spectrum.InChI,
                        Ontology = spectrum.Ontology,
                        IUPACName = spectrum.IUPACname,
                        CID = spectrum.CID,
                        CAS = spectrum.CAS,
                        ExactMass = spectrum.Exactmass,
                        MW = spectrum.MW,
                        XLogP = spectrum.XLogP,
                        MLogP = spectrum.MLogP,
                        ALogP = spectrum.ALogP,
                        NumBond = spectrum.NumBond,
                        NumAtom = spectrum.NumAtom,
                        Superclass = spectrum.Superclass,
                        Class = spectrum.Class,
                        SVHC = spectrum.SVHC,
                        CMR = spectrum.CMR,
                        CMRSuspect = spectrum.CMRsuspect,
                        EDC = spectrum.EDC,
                        IARC = spectrum.IARC,
                        Eusml = spectrum.Eusml,
                        ChinaSml = spectrum.ChinaSml,
                        // Additional metadata
                        Authors = spectrum.Authors,
                        Instrument = spectrum.Instrument,
                        InstrumentType = spectrum.InstrumentType,
                        CollisionEnergy = spectrum.CollisionEnergy,
                        Date = spectrum.Date ?? "",
                        Num_peaks = spectrum.PeakNumber,
                        TopoPSA = spectrum.TopoPSA,
                        // Additional toxicological properties
                        Carcinogenicity_ISS = spectrum.Carcinogenicity_ISS,
                        DNA_alerts_OASIS = spectrum.DNA_alerts_OASIS,
                        DNA_binding_OASIS = spectrum.DNA_binding_OASIS,
                        DNA_binding_OECD = spectrum.DNA_binding_OECD,
                        Protein_binding_alerts_OASIS = spectrum.Protein_binding_alerts_OASIS,
                        Vitro_mutagenicity_alerts_ISS = spectrum.Vitro_mutagenicity_alerts_ISS,
                        Vivo_mutagenicity_alerts_ISS = spectrum.Vivo_mutagenicity_alerts_ISS
                    };
                    comp.NumSpectra++;
                    comp.Spectra.Add(spectrum);
                    comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                    comp.Name = spectrum.Name;
                    comp.Formula = spectrum.Formula;
                    comp.Smiles = spectrum.Smiles;
                    System.Diagnostics.Debug.WriteLine($"DEBUG: Created CompoundBean - Name: {comp.Name}, InstrumentType: '{comp.InstrumentType}', CollisionEnergy: {comp.CollisionEnergy}");
                    dic.Add(counter.ToString(), comp);
                    counter++;
                }
                else
                {
                    var Inchikey = spectrum.InChIKey.Split('-')[0];
                    if (InchiKeys.Contains(Inchikey))
                    {
                        dic[Inchikey].Spectra.Add(spectrum);
                        dic[Inchikey].NumSpectra++;
                    }
                    else
                    {
                        comp = new CompoundBean
                        {
                            Id = counter,
                            InChIKey = spectrum.InChIKey,
                            InChI = spectrum.InChI,
                            Ontology = spectrum.Ontology,
                            IUPACName = spectrum.IUPACname,
                            CID = spectrum.CID,
                            CAS = spectrum.CAS,
                            ExactMass = spectrum.Exactmass,
                            MW = spectrum.MW,
                            XLogP = spectrum.XLogP,
                            MLogP = spectrum.MLogP,
                            ALogP = spectrum.ALogP,
                            NumBond = spectrum.NumBond,
                            NumAtom = spectrum.NumAtom,
                            Superclass = spectrum.Superclass,
                            Class = spectrum.Class,
                            Subclass = spectrum.Subclass,
                            Cramerrules = spectrum.Cramerrules,
                            SVHC = spectrum.SVHC,
                            CMR = spectrum.CMR,
                            CMRSuspect = spectrum.CMRsuspect,
                            EDC = spectrum.EDC,
                            IARC = spectrum.IARC,
                            Eusml = spectrum.Eusml,
                            ChinaSml = spectrum.ChinaSml,
                            // Critical MS data properties (MISSING ASSIGNMENTS FIXED)
                            PrecursorMz = spectrum.PrecursorMz,
                            IonMode = spectrum.IonMode.ToString(),
                            PrecursorType = spectrum.AdductIon?.AdductIonName ?? "",
                            // Additional metadata
                            Authors = spectrum.Authors,
                            Instrument = spectrum.Instrument,
                            InstrumentType = spectrum.InstrumentType,
                            CollisionEnergy = spectrum.CollisionEnergy,
                            Date = spectrum.Date ?? "",
                            Num_peaks = spectrum.PeakNumber,
                            TopoPSA = spectrum.TopoPSA,
                            // Additional toxicological properties
                            Carcinogenicity_ISS = spectrum.Carcinogenicity_ISS,
                            DNA_alerts_OASIS = spectrum.DNA_alerts_OASIS,
                            DNA_binding_OASIS = spectrum.DNA_binding_OASIS,
                            DNA_binding_OECD = spectrum.DNA_binding_OECD,
                            Protein_binding_alerts_OASIS = spectrum.Protein_binding_alerts_OASIS,
                            Vitro_mutagenicity_alerts_ISS = spectrum.Vitro_mutagenicity_alerts_ISS,
                            Vivo_mutagenicity_alerts_ISS = spectrum.Vivo_mutagenicity_alerts_ISS
                        };
                        comp.NumSpectra++;
                        comp.Spectra.Add(spectrum);
                        comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                        comp.Name = spectrum.Name;
                        comp.Formula = spectrum.Formula;
                        comp.Smiles = spectrum.Smiles;
                        dic.Add(Inchikey, comp);
                        InchiKeys.Add(Inchikey);
                        counter++;
                    }
                }
            }
            return new List<CompoundBean>(dic.Values);
        }


        public static List<CompoundBean> CreateCompoundListByInChIKey(List<MassSpectrum> rawLibraryFile)
        {
            CompoundBean comp;
            var dic = new Dictionary<string, CompoundBean>();
            var InchiKeys = new List<string>();

            var counter = 1;
            foreach (var spectrum in rawLibraryFile)
            {
                if (string.IsNullOrEmpty(spectrum.InChIKey))
                {
                    comp = new CompoundBean
                    {
                        Id = counter,
                        InChIKey = "",
                        InChI = spectrum.InChI,
                        Ontology = spectrum.Ontology,
                        IUPACName = spectrum.IUPACname,
                        CID = spectrum.CID,
                        CAS = spectrum.CAS,
                        ExactMass = spectrum.Exactmass,
                        MW = spectrum.MW,
                        XLogP = spectrum.XLogP,
                        MLogP = spectrum.MLogP,
                        ALogP = spectrum.ALogP,
                        NumBond = spectrum.NumBond,
                        NumAtom = spectrum.NumAtom,
                        Superclass = spectrum.Superclass,
                        Class = spectrum.Class,
                        SVHC = spectrum.SVHC,
                        CMR = spectrum.CMR,
                        CMRSuspect = spectrum.CMRsuspect,
                        EDC = spectrum.EDC,
                        IARC = spectrum.IARC,
                        Eusml = spectrum.Eusml,
                        ChinaSml = spectrum.ChinaSml
                    };
                    comp.NumSpectra++;
                    comp.Spectra.Add(spectrum);
                    comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                    comp.Name = spectrum.Name;
                    comp.Formula = spectrum.Formula;
                    comp.Smiles = spectrum.Smiles;
                    dic.Add(counter.ToString(), comp);
                    counter++;
                }
                else
                {
                    var InChIKey = spectrum.InChIKey;
                    if (InchiKeys.Contains(InChIKey))
                    {
                        dic[InChIKey].Spectra.Add(spectrum);
                        dic[InChIKey].NumSpectra++;
                    }
                    else
                    {
                        comp = new CompoundBean
                        {
                            Id = counter,
                            InChIKey = spectrum.InChIKey,
                            InChI = spectrum.InChI,
                            Ontology = spectrum.Ontology,
                            IUPACName = spectrum.IUPACname,
                            CID = spectrum.CID,
                            CAS = spectrum.CAS,
                            ExactMass = spectrum.Exactmass,
                            MW = spectrum.MW,
                            XLogP = spectrum.XLogP,
                            MLogP = spectrum.MLogP,
                            ALogP = spectrum.ALogP,
                            NumBond = spectrum.NumBond,
                            NumAtom = spectrum.NumAtom,
                            Superclass = spectrum.Superclass,
                            Class = spectrum.Class,
                            Subclass = spectrum.Subclass,
                            Cramerrules = spectrum.Cramerrules,
                            SVHC = spectrum.SVHC,
                            CMR = spectrum.CMR,
                            CMRSuspect = spectrum.CMRsuspect,
                            EDC = spectrum.EDC,
                            IARC = spectrum.IARC,
                            Eusml = spectrum.Eusml,
                            ChinaSml = spectrum.ChinaSml,
                            // Critical MS data properties (MISSING ASSIGNMENTS FIXED)
                            PrecursorMz = spectrum.PrecursorMz,
                            IonMode = spectrum.IonMode.ToString(),
                            PrecursorType = spectrum.AdductIon?.AdductIonName ?? "",
                            // Additional metadata
                            Authors = spectrum.Authors,
                            Instrument = spectrum.Instrument,
                            InstrumentType = spectrum.InstrumentType,
                            CollisionEnergy = spectrum.CollisionEnergy,
                            Date = spectrum.Date ?? "",
                            Num_peaks = spectrum.PeakNumber,
                            TopoPSA = spectrum.TopoPSA,
                            // Additional toxicological properties
                            Carcinogenicity_ISS = spectrum.Carcinogenicity_ISS,
                            DNA_alerts_OASIS = spectrum.DNA_alerts_OASIS,
                            DNA_binding_OASIS = spectrum.DNA_binding_OASIS,
                            DNA_binding_OECD = spectrum.DNA_binding_OECD,
                            Protein_binding_alerts_OASIS = spectrum.Protein_binding_alerts_OASIS,
                            Vitro_mutagenicity_alerts_ISS = spectrum.Vitro_mutagenicity_alerts_ISS,
                            Vivo_mutagenicity_alerts_ISS = spectrum.Vivo_mutagenicity_alerts_ISS
                        };
                        comp.NumSpectra++;
                        comp.Spectra.Add(spectrum);
                        comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                        comp.Name = spectrum.Name;
                        comp.Formula = spectrum.Formula;
                        comp.Smiles = spectrum.Smiles;
                        dic.Add(InChIKey, comp);
                        InchiKeys.Add(InChIKey);
                        counter++;
                    }
                }
            }
            return new List<CompoundBean>(dic.Values);
        }


        public static List<CompoundBean> CreateCompoundListByInChI(List<MassSpectrum> rawLibraryFile)
        {
            CompoundBean comp;
            var dic = new Dictionary<string, CompoundBean>();
            var InChIs = new List<string>();

            var counter = 1;
            foreach (var spectrum in rawLibraryFile)
            {
                if (string.IsNullOrEmpty(spectrum.InChI))
                {
                    comp = new CompoundBean
                    {
                        Id = counter,
                        InChIKey = spectrum.InChIKey,
                        InChI = "",
                        Ontology = spectrum.Ontology,
                        IUPACName = spectrum.IUPACname,
                        CID = spectrum.CID,
                        CAS = spectrum.CAS,
                        ExactMass = spectrum.Exactmass,
                        MW = spectrum.MW,
                        XLogP = spectrum.XLogP,
                        MLogP = spectrum.MLogP,
                        ALogP = spectrum.ALogP,
                        NumBond = spectrum.NumBond,
                        NumAtom = spectrum.NumAtom,
                        Superclass = spectrum.Superclass,
                        Class = spectrum.Class,
                        Subclass = spectrum.Subclass,
                        Cramerrules = spectrum.Cramerrules,
                        SVHC = spectrum.SVHC,
                        CMR = spectrum.CMR,
                        CMRSuspect = spectrum.CMRsuspect,
                        EDC = spectrum.EDC,
                        IARC = spectrum.IARC,
                        Eusml = spectrum.Eusml,
                        ChinaSml = spectrum.ChinaSml,
                        // Critical MS data properties (MISSING ASSIGNMENTS FIXED)
                        PrecursorMz = spectrum.PrecursorMz,
                        IonMode = spectrum.IonMode.ToString(),
                        PrecursorType = spectrum.AdductIon?.AdductIonName ?? "",
                        // Additional metadata
                        Authors = spectrum.Authors,
                        Instrument = spectrum.Instrument,
                        InstrumentType = spectrum.InstrumentType,
                        CollisionEnergy = spectrum.CollisionEnergy,
                        Date = spectrum.Date ?? "",
                        Num_peaks = spectrum.PeakNumber,
                        TopoPSA = spectrum.TopoPSA,
                        // Additional toxicological properties
                        Carcinogenicity_ISS = spectrum.Carcinogenicity_ISS,
                        DNA_alerts_OASIS = spectrum.DNA_alerts_OASIS,
                        DNA_binding_OASIS = spectrum.DNA_binding_OASIS,
                        DNA_binding_OECD = spectrum.DNA_binding_OECD,
                        Protein_binding_alerts_OASIS = spectrum.Protein_binding_alerts_OASIS,
                        Vitro_mutagenicity_alerts_ISS = spectrum.Vitro_mutagenicity_alerts_ISS,
                        Vivo_mutagenicity_alerts_ISS = spectrum.Vivo_mutagenicity_alerts_ISS
                    };
                    comp.NumSpectra++;
                    comp.Spectra.Add(spectrum);
                    comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                    comp.Name = spectrum.Name;
                    comp.Formula = spectrum.Formula;
                    comp.Smiles = spectrum.Smiles;
                    dic.Add(counter.ToString(), comp);
                    counter++;
                }
                else
                {
                    var InChI = spectrum.InChI;
                    if (InChIs.Contains(InChI))
                    {
                        dic[InChI].Spectra.Add(spectrum);
                        dic[InChI].NumSpectra++;
                    }
                    else
                    {
                        comp = new CompoundBean
                        {
                            Id = counter,
                            InChIKey = spectrum.InChIKey,
                            InChI = spectrum.InChI,
                            Ontology = spectrum.Ontology,
                            IUPACName = spectrum.IUPACname,
                            CID = spectrum.CID,
                            CAS = spectrum.CAS,
                            ExactMass = spectrum.Exactmass,
                            MW = spectrum.MW,
                            XLogP = spectrum.XLogP,
                            MLogP = spectrum.MLogP,
                            ALogP = spectrum.ALogP,
                            NumBond = spectrum.NumBond,
                            NumAtom = spectrum.NumAtom,
                            Superclass = spectrum.Superclass,
                            Class = spectrum.Class,
                            Subclass = spectrum.Subclass,
                            Cramerrules = spectrum.Cramerrules,
                            SVHC = spectrum.SVHC,
                            CMR = spectrum.CMR,
                            CMRSuspect = spectrum.CMRsuspect,
                            EDC = spectrum.EDC,
                            IARC = spectrum.IARC,
                            Eusml = spectrum.Eusml,
                            ChinaSml = spectrum.ChinaSml,
                            // Critical MS data properties (MISSING ASSIGNMENTS FIXED)
                            PrecursorMz = spectrum.PrecursorMz,
                            IonMode = spectrum.IonMode.ToString(),
                            PrecursorType = spectrum.AdductIon?.AdductIonName ?? "",
                            // Additional metadata
                            Authors = spectrum.Authors,
                            Instrument = spectrum.Instrument,
                            InstrumentType = spectrum.InstrumentType,
                            CollisionEnergy = spectrum.CollisionEnergy,
                            Date = spectrum.Date ?? "",
                            Num_peaks = spectrum.PeakNumber,
                            TopoPSA = spectrum.TopoPSA,
                            // Additional toxicological properties
                            Carcinogenicity_ISS = spectrum.Carcinogenicity_ISS,
                            DNA_alerts_OASIS = spectrum.DNA_alerts_OASIS,
                            DNA_binding_OASIS = spectrum.DNA_binding_OASIS,
                            DNA_binding_OECD = spectrum.DNA_binding_OECD,
                            Protein_binding_alerts_OASIS = spectrum.Protein_binding_alerts_OASIS,
                            Vitro_mutagenicity_alerts_ISS = spectrum.Vitro_mutagenicity_alerts_ISS,
                            Vivo_mutagenicity_alerts_ISS = spectrum.Vivo_mutagenicity_alerts_ISS
                        };
                        comp.NumSpectra++;
                        comp.Spectra.Add(spectrum);
                        comp.MolecularWeight = FormulaUtility.GetMass(spectrum.Formula);
                        comp.Name = spectrum.Name;
                        comp.Formula = spectrum.Formula;
                        comp.Smiles = spectrum.Smiles;
                        dic.Add(InChI, comp);
                        InChIs.Add(InChI);
                        counter++;
                    }
                }
            }
            return new List<CompoundBean>(dic.Values);
        }

        public static void CheckCompoundList(List<CompoundBean> compounds, float minRtDiff, ref string rtString, ref string formulaString, ref string InChIKeyString)
        {
            var missFormula = new List<string>();
            var missInChIKeys = new List<string>();
            var missRts = new List<string>();
            foreach (var compound in compounds)
            {
                var formulas = compound.Spectra.Select(x => x.Formula).Distinct().ToList();
                if (formulas.Count > 1)
                {
                    var str = "ID: " + compound.Id + ", Name: " + compound.Name + ", Formulas: ";
                    foreach (var f in formulas) { str = str + ", " + f; }
                    missFormula.Add(str);
                }

                var InChIKey = compound.Spectra.Select(x => x.InChIKey).Distinct().ToList();
                if (InChIKey.Count > 1)
                {
                    var counter2 = 0;
                    foreach (var i in InChIKey)
                    {
                        if (!i.Contains("-UHFFFAOYSA -")) counter2++;
                    }
                    if (counter2 > 1)
                    {
                        var str = "ID: " + compound.Id + ", Name: " + compound.Name + ", InChIKeys: ";
                        foreach (var s in InChIKey) { str = str + ", " + s; }
                        missInChIKeys.Add(str);
                    }
                }
                var rtOriginal = compound.Spectra.Select(x => x.RetentionTime);
                var rts = new List<float>();
                foreach (var item in rtOriginal)
                {
                    rts.Add((float)Math.Round(item, 2));
                }
                rts = rts.Distinct().ToList();
                string str2 = "";
                var maxRt = -1f; var minRt = 100000f;
                for (var i = 0; i < rts.Count; i++)
                {
                    if (rts[i] < 0) continue;
                    if (maxRt < rts[i])
                    {
                        maxRt = rts[i];
                    }
                    if (rts[i] < minRt)
                    {
                        minRt = rts[i];
                    }
                }
                if (minRt < 100000f && maxRt > -1f && minRt < maxRt && (maxRt - minRt) > minRtDiff)
                {
                    str2 = "ID: " + compound.Id + ", Name: " + compound.Name + ", first RT: " + rts[0] + ", minRT: " + minRt + ", maxRT: " + maxRt + ", diff: " + (maxRt - minRt);
                    missRts.Add(str2);
                }
            }
            if (missRts.Count > 0)
            {
                rtString = string.Join("\n", missRts);
            }
            if (missFormula.Count > 0)
            {
                formulaString = string.Join("\n", missFormula);
            }
            if (missInChIKeys.Count > 0)
            {
                InChIKeyString = string.Join("\n", missInChIKeys);
            }
        }

        public static void ConvertActualMassToTheoreticalMass(List<CompoundBean> compounds)
        {
            foreach (var comp in compounds) {
                foreach (var spec in comp.Spectra) {
                    MassSpectrumUtility.ConvertActualMassToTheoreticalMass(spec);
                }
            }
        }

        public static void DropRetentionTime(List<CompoundBean> compounds)
        {
            foreach (var comp in compounds)
            {
                foreach (var spec in comp.Spectra)
                {
                    MassSpectrumUtility.DropRetentionTime(spec);
                }
            }
        }

        public static void RemoveUnannotatedPeaks(List<CompoundBean> compounds)
        {
            foreach (var comp in compounds)
            {
                foreach (var spec in comp.Spectra)
                {
                    MassSpectrumUtility.RemoveUnannotatedPeaks(spec);
                }
            }
        }

        public static void UpdateMetaData(List<CompoundBean> compounds, List<TemporaryFile> temporaryInfo, out List<string> errorMessage)
        {
            var inChIKeyDict = new Dictionary<string, TemporaryFile>();
            errorMessage = new List<string>();

            foreach (var row in temporaryInfo)
            {
                inChIKeyDict.Add(row.InChIKey, row);
            }
            foreach (var comp in compounds)
            {
                if (inChIKeyDict.Keys.Contains(comp.InChIKey))
                {
                    comp.InChI = inChIKeyDict[comp.InChIKey].InChI;
                    comp.Smiles = inChIKeyDict[comp.InChIKey].SMILES;
                    foreach (var spectrum in comp.Spectra)
                    {
                        if (spectrum.InChIKey == comp.InChIKey)
                        {
                            spectrum.InChI = inChIKeyDict[comp.InChIKey].InChI;
                            spectrum.Smiles = inChIKeyDict[comp.InChIKey].SMILES;
                        }
                        else if (inChIKeyDict.Keys.Contains(spectrum.InChIKey))
                        {
                            spectrum.InChI = inChIKeyDict[spectrum.InChIKey].InChI;
                            spectrum.Smiles = inChIKeyDict[spectrum.InChIKey].SMILES;
                        }
                        else if (spectrum.InChIKey.Contains("-UHFFFAOYSA-"))
                        {
                            spectrum.InChI = inChIKeyDict[comp.InChIKey].InChI;
                            spectrum.Smiles = inChIKeyDict[comp.InChIKey].SMILES;
                        }
                        else
                        {
                            errorMessage.Add("Spectrum ID: " + spectrum.Id + ": " + spectrum.InChIKey + ", " + spectrum.Name + "\n");
                        }
                    }
                }
                else
                {
                    errorMessage.Add("Componud ID: " + comp.Id + ": " + comp.InChIKey + ", " + comp.Name + "\n");
                }
            }
        }
    }
}